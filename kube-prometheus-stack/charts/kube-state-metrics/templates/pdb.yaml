{{- if .Values.podDisruptionBudget -}}
{{ if $.Capabilities.APIVersions.Has "policy/v1/PodDisruptionBudget" -}}
apiVersion: policy/v1
{{- else -}}
apiVersion: policy/v1beta1
{{- end }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "kube-state-metrics.fullname" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ template "kube-state-metrics.name" . }}
{{ toYaml .Values.podDisruptionBudget | indent 2 }}
{{- end -}}
