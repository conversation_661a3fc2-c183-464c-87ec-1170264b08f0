apiVersion: v1
kind: Service
metadata:
  name: {{ template "kube-state-metrics.fullname" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  annotations:
    {{- if .Values.prometheusScrape }}
    prometheus.io/scrape: '{{ .Values.prometheusScrape }}'
    {{- end }}
    {{- if .Values.service.annotations }}
    {{- toYaml .Values.service.annotations | nindent 4 }}
    {{- end }}
spec:
  type: "{{ .Values.service.type }}"
  {{- if .Values.service.ipDualStack.enabled }}
  ipFamilies: {{ toYaml .Values.service.ipDualStack.ipFamilies | nindent 4 }}
  ipFamilyPolicy: {{ .Values.service.ipDualStack.ipFamilyPolicy }}
  {{- end }}
  ports:
  - name: "http"
    protocol: TCP
    port: {{ .Values.service.port | default 8080}}
  {{- if .Values.service.nodePort }}
    nodePort: {{ .Values.service.nodePort }}
  {{- end }}
    targetPort: {{ .Values.service.port | default 8080}}
  {{ if .Values.selfMonitor.enabled }}
  - name: "metrics"
    protocol: TCP
    port: {{ .Values.selfMonitor.telemetryPort | default 8081 }}
    targetPort: {{ .Values.selfMonitor.telemetryPort | default 8081 }}
  {{- if .Values.selfMonitor.telemetryNodePort }}
    nodePort: {{ .Values.selfMonitor.telemetryNodePort }}
  {{- end }}
  {{ end }}
{{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: "{{ .Values.service.loadBalancerIP }}"
{{- end }}
{{- if .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
  {{- range $cidr := .Values.service.loadBalancerSourceRanges }}
    - {{ $cidr }}
  {{- end }}
{{- end }}
{{- if .Values.autosharding.enabled }}
  clusterIP: None
{{- else if .Values.service.clusterIP }}
  clusterIP: "{{ .Values.service.clusterIP }}"
{{- end }}
  selector:
    {{- include "kube-state-metrics.selectorLabels" . | indent 4 }}
