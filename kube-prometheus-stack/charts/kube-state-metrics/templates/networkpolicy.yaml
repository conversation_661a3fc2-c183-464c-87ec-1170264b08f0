{{- if and .Values.networkPolicy.enabled (eq .Values.networkPolicy.flavor "kubernetes") }}
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  {{- if .Values.annotations }}
  annotations:
    {{ toYaml .Values.annotations | nindent 4 }}
  {{- end }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  name: {{ template "kube-state-metrics.fullname" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
spec:
  {{- if .Values.networkPolicy.egress }}
  ## Deny all egress by default
  egress:
    {{- toYaml .Values.networkPolicy.egress | nindent 4 }}
  {{- end }}
  ingress:
  {{- if .Values.networkPolicy.ingress }}
    {{- toYaml .Values.networkPolicy.ingress | nindent 4 }}
  {{- else }}
    ## Allow ingress on default ports by default
  - ports:
    - port: {{ .Values.service.port | default 8080 }}
      protocol: TCP
    {{- if .Values.selfMonitor.enabled }}
    {{- $telemetryPort := ternary 9091 (.Values.selfMonitor.telemetryPort | default 8081) .Values.kubeRBACProxy.enabled}}
    - port: {{ $telemetryPort }}
      protocol: TCP
    {{- end }}
  {{- end }}
  podSelector:
    {{- if .Values.networkPolicy.podSelector }}
    {{- toYaml .Values.networkPolicy.podSelector | nindent 4 }}
    {{- else }}
    matchLabels:
      {{- include "kube-state-metrics.selectorLabels" . | indent 6 }}
    {{- end }}
  policyTypes:
    - Ingress
    - Egress
{{- end }}
