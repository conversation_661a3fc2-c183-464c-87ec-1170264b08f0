{{- if and (eq .Values.rbac.create true) (not .Values.rbac.useExistingRole) -}}
{{- range (ternary (join "," .Values.namespaces | split "," ) (list "") (eq $.Values.rbac.useClusterRole false)) }}
---
apiVersion: rbac.authorization.k8s.io/v1
{{- if eq $.Values.rbac.useClusterRole false }}
kind: Role
{{- else }}
kind: ClusterRole
{{- end }}
metadata:
  labels:
    {{- include "kube-state-metrics.labels" $ | indent 4 }}
  name: {{ template "kube-state-metrics.fullname" $ }}
{{- if eq $.Values.rbac.useClusterRole false }}
  namespace: {{ . }}
{{- end }}
rules:
{{ if has "certificatesigningrequests" $.Values.collectors }}
- apiGroups: ["certificates.k8s.io"]
  resources:
  - certificatesigningrequests
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "configmaps" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - configmaps
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "cronjobs" $.Values.collectors }}
- apiGroups: ["batch"]
  resources:
  - cronjobs
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "daemonsets" $.Values.collectors }}
- apiGroups: ["extensions", "apps"]
  resources:
  - daemonsets
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "deployments" $.Values.collectors }}
- apiGroups: ["extensions", "apps"]
  resources:
  - deployments
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "endpoints" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - endpoints
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "endpointslices" $.Values.collectors }}
- apiGroups: ["discovery.k8s.io"]
  resources:
  - endpointslices
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "horizontalpodautoscalers" $.Values.collectors }}
- apiGroups: ["autoscaling"]
  resources:
  - horizontalpodautoscalers
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "ingresses" $.Values.collectors }}
- apiGroups: ["extensions", "networking.k8s.io"]
  resources:
  - ingresses
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "jobs" $.Values.collectors }}
- apiGroups: ["batch"]
  resources:
  - jobs
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "leases" $.Values.collectors }}
- apiGroups: ["coordination.k8s.io"]
  resources:
  - leases
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "limitranges" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - limitranges
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "mutatingwebhookconfigurations" $.Values.collectors }}
- apiGroups: ["admissionregistration.k8s.io"]
  resources:
    - mutatingwebhookconfigurations
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "namespaces" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - namespaces
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "networkpolicies" $.Values.collectors }}
- apiGroups: ["networking.k8s.io"]
  resources:
  - networkpolicies
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "ingressclasses" $.Values.collectors }}
- apiGroups: ["networking.k8s.io"]
  resources:
  - ingressclasses
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "clusterrolebindings" $.Values.collectors }}
- apiGroups: ["rbac.authorization.k8s.io"]
  resources:
  - clusterrolebindings
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "clusterroles" $.Values.collectors }}
- apiGroups: ["rbac.authorization.k8s.io"]
  resources:
  - clusterroles
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "roles" $.Values.collectors }}
- apiGroups: ["rbac.authorization.k8s.io"]
  resources:
  - roles
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "nodes" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - nodes
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "persistentvolumeclaims" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - persistentvolumeclaims
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "persistentvolumes" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - persistentvolumes
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "poddisruptionbudgets" $.Values.collectors }}
- apiGroups: ["policy"]
  resources:
    - poddisruptionbudgets
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "pods" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - pods
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "replicasets" $.Values.collectors }}
- apiGroups: ["extensions", "apps"]
  resources:
  - replicasets
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "replicationcontrollers" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - replicationcontrollers
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "resourcequotas" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - resourcequotas
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "secrets" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - secrets
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "services" $.Values.collectors }}
- apiGroups: [""]
  resources:
  - services
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "statefulsets" $.Values.collectors }}
- apiGroups: ["apps"]
  resources:
  - statefulsets
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "storageclasses" $.Values.collectors }}
- apiGroups: ["storage.k8s.io"]
  resources:
    - storageclasses
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "validatingwebhookconfigurations" $.Values.collectors }}
- apiGroups: ["admissionregistration.k8s.io"]
  resources:
    - validatingwebhookconfigurations
  verbs: ["list", "watch"]
{{ end -}}
{{ if has "volumeattachments" $.Values.collectors }}
- apiGroups: ["storage.k8s.io"]
  resources:
    - volumeattachments
  verbs: ["list", "watch"]
{{ end -}}
{{-  if $.Values.kubeRBACProxy.enabled  }}
- apiGroups: ["authentication.k8s.io"]
  resources:
    - tokenreviews
  verbs: ["create"]
- apiGroups: ["authorization.k8s.io"]
  resources:
    - subjectaccessreviews
  verbs: ["create"]
{{- end }}
{{- if $.Values.customResourceState.enabled }}
- apiGroups: ["apiextensions.k8s.io"]
  resources:
    - customresourcedefinitions
  verbs: ["list", "watch"]
{{- end }}
{{ if $.Values.rbac.extraRules }}
{{ toYaml $.Values.rbac.extraRules }}
{{ end }}
{{- end -}}
{{- end -}}
