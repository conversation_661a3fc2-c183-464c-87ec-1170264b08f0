{{- if and .Values.podSecurityPolicy.enabled (.Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy") }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  name: psp-{{ template "kube-state-metrics.fullname" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: psp-{{ template "kube-state-metrics.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "kube-state-metrics.serviceAccountName" . }}
    namespace: {{ template "kube-state-metrics.namespace" . }}
{{- end }}
