{{- if .Values.prometheus.monitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "kube-state-metrics.fullname" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  {{- with .Values.prometheus.monitor.additionalLabels }}
    {{- tpl (toYaml . | nindent 4) $ }}
  {{- end }}
  {{- with .Values.prometheus.monitor.annotations }}
  annotations:
    {{- tpl (toYaml . | nindent 4) $ }}
  {{- end }}
spec:
  jobLabel: {{ default "app.kubernetes.io/name" .Values.prometheus.monitor.jobLabel }}
  {{- with .Values.prometheus.monitor.targetLabels }}
  targetLabels:
    {{- toYaml . | trim | nindent 4 }}
  {{- end }}
  {{- with .Values.prometheus.monitor.podTargetLabels }}
  podTargetLabels:
    {{- toYaml . | trim | nindent 4 }}
  {{- end }}
  {{- include "servicemonitor.scrapeLimits" .Values.prometheus.monitor | indent 2 }}
  {{- if .Values.prometheus.monitor.namespaceSelector }}
  namespaceSelector:
    matchNames:
    {{- with .Values.prometheus.monitor.namespaceSelector }}
      {{- toYaml . | nindent 6 }}
    {{- end }}
  {{- end }}
  selector:
    matchLabels:
    {{- with .Values.prometheus.monitor.selectorOverride }}
      {{- toYaml . | nindent 6 }}
    {{- else }}
      {{- include "kube-state-metrics.selectorLabels" . | indent 6 }}
    {{- end }}
  endpoints:
    - port: http
    {{- if or .Values.prometheus.monitor.http.interval .Values.prometheus.monitor.interval }}
      interval: {{ .Values.prometheus.monitor.http.interval | default .Values.prometheus.monitor.interval }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.http.scrapeTimeout .Values.prometheus.monitor.scrapeTimeout }}
      scrapeTimeout: {{ .Values.prometheus.monitor.http.scrapeTimeout | default .Values.prometheus.monitor.scrapeTimeout }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.http.proxyUrl .Values.prometheus.monitor.proxyUrl }}
      proxyUrl: {{ .Values.prometheus.monitor.http.proxyUrl | default .Values.prometheus.monitor.proxyUrl }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.http.enableHttp2 .Values.prometheus.monitor.enableHttp2 }}
      enableHttp2: {{ .Values.prometheus.monitor.http.enableHttp2 | default .Values.prometheus.monitor.enableHttp2 }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.http.honorLabels .Values.prometheus.monitor.honorLabels }}
      honorLabels: true
    {{- end }}
    {{- if or .Values.prometheus.monitor.http.metricRelabelings .Values.prometheus.monitor.metricRelabelings }}
      metricRelabelings:
        {{- toYaml (.Values.prometheus.monitor.http.metricRelabelings | default .Values.prometheus.monitor.metricRelabelings) | nindent 8 }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.http.relabelings .Values.prometheus.monitor.relabelings }}
      relabelings:
        {{- toYaml (.Values.prometheus.monitor.http.relabelings | default .Values.prometheus.monitor.relabelings) | nindent 8 }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.http.scheme .Values.prometheus.monitor.scheme }}
      scheme: {{ .Values.prometheus.monitor.http.scheme | default .Values.prometheus.monitor.scheme }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.http.tlsConfig .Values.prometheus.monitor.tlsConfig }}
      tlsConfig:
        {{- toYaml (.Values.prometheus.monitor.http.tlsConfig | default .Values.prometheus.monitor.tlsConfig) | nindent 8 }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.http.bearerTokenFile .Values.prometheus.monitor.bearerTokenFile }}
      bearerTokenFile: {{ .Values.prometheus.monitor.http.bearerTokenFile | default .Values.prometheus.monitor.bearerTokenFile }}
    {{- end }}
    {{- with (.Values.prometheus.monitor.http.bearerTokenSecret | default .Values.prometheus.monitor.bearerTokenSecret) }}
      bearerTokenSecret:
        {{- toYaml . | nindent 8 }}
    {{- end }}
  {{- if .Values.selfMonitor.enabled }}
    - port: metrics
    {{- if or .Values.prometheus.monitor.metrics.interval .Values.prometheus.monitor.interval }}
      interval: {{ .Values.prometheus.monitor.metrics.interval | default .Values.prometheus.monitor.interval }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.metrics.scrapeTimeout .Values.prometheus.monitor.scrapeTimeout }}
      scrapeTimeout: {{ .Values.prometheus.monitor.metrics.scrapeTimeout | default .Values.prometheus.monitor.scrapeTimeout }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.metrics.proxyUrl .Values.prometheus.monitor.proxyUrl }}
      proxyUrl: {{ .Values.prometheus.monitor.metrics.proxyUrl | default .Values.prometheus.monitor.proxyUrl }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.metrics.enableHttp2 .Values.prometheus.monitor.enableHttp2 }}
      enableHttp2: {{ .Values.prometheus.monitor.metrics.enableHttp2 | default .Values.prometheus.monitor.enableHttp2 }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.metrics.honorLabels .Values.prometheus.monitor.honorLabels }}
      honorLabels: true
    {{- end }}
    {{- if or .Values.prometheus.monitor.metrics.metricRelabelings .Values.prometheus.monitor.metricRelabelings }}
      metricRelabelings:
        {{- toYaml (.Values.prometheus.monitor.metrics.metricRelabelings | default .Values.prometheus.monitor.metricRelabelings) | nindent 8 }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.metrics.relabelings .Values.prometheus.monitor.relabelings }}
      relabelings:
        {{- toYaml (.Values.prometheus.monitor.metrics.relabelings | default .Values.prometheus.monitor.relabelings) | nindent 8 }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.metrics.scheme .Values.prometheus.monitor.scheme }}
      scheme: {{ .Values.prometheus.monitor.metrics.scheme | default .Values.prometheus.monitor.scheme }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.metrics.tlsConfig .Values.prometheus.monitor.tlsConfig }}
      tlsConfig:
        {{- toYaml (.Values.prometheus.monitor.metrics.tlsConfig | default .Values.prometheus.monitor.tlsConfig) | nindent 8 }}
    {{- end }}
    {{- if or .Values.prometheus.monitor.metrics.bearerTokenFile .Values.prometheus.monitor.bearerTokenFile }}
      bearerTokenFile: {{ .Values.prometheus.monitor.metrics.bearerTokenFile | default .Values.prometheus.monitor.bearerTokenFile }}
    {{- end }}
    {{- with (.Values.prometheus.monitor.metrics.bearerTokenSecret | default .Values.prometheus.monitor.bearerTokenSecret) }}
      bearerTokenSecret:
        {{- toYaml . | nindent 8 }}
    {{- end }}
  {{- end }}
{{- end }}
