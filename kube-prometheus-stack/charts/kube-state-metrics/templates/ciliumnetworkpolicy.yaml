{{- if and .Values.networkPolicy.enabled (eq .Values.networkPolicy.flavor "cilium") }}
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  {{- if .Values.annotations }}
  annotations:
    {{ toYaml .Values.annotations | nindent 4 }}
  {{- end }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  name: {{ template "kube-state-metrics.fullname" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
spec:
  endpointSelector:
    matchLabels:
      {{- include "kube-state-metrics.selectorLabels" . | indent 6 }}
  egress:
    {{- if and .Values.networkPolicy.cilium .Values.networkPolicy.cilium.kubeApiServerSelector }}
      {{ toYaml .Values.networkPolicy.cilium.kubeApiServerSelector | nindent 6 }}
    {{- else }}
    - toEntities:
      - kube-apiserver
    {{- end }}
  ingress:
  - toPorts:
    - ports:
      - port: {{ .Values.service.port | quote }}
        protocol: TCP
      {{- if .Values.selfMonitor.enabled }}
      - port: {{ .Values.selfMonitor.telemetryPort | default 8081 | quote }}
        protocol: TCP
      {{ end }}
{{ end }}
