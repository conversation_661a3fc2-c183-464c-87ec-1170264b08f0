apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "prometheus-windows-exporter.fullname" . }}
  namespace: {{ include "prometheus-windows-exporter.namespace" . }}
  labels:
    {{- include "prometheus-windows-exporter.labels" $ | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  config.yml: |
    {{- .Values.config | nindent 4 }}
