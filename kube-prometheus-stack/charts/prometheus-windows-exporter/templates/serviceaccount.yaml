{{- if and .Values.rbac.create .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "prometheus-windows-exporter.serviceAccountName" . }}
  namespace: {{ include "prometheus-windows-exporter.namespace" . }}
  labels:
    {{- include "prometheus-windows-exporter.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- if or .Values.serviceAccount.imagePullSecrets .Values.global.imagePullSecrets }}
imagePullSecrets:
  {{- include "prometheus-windows-exporter.imagePullSecrets" (dict "Values" .Values "imagePullSecrets" .Values.serviceAccount.imagePullSecrets) | indent 2 }}
{{- end }}
{{- end -}}
