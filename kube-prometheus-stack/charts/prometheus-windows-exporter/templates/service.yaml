apiVersion: v1
kind: Service
metadata:
  name: {{ include "prometheus-windows-exporter.fullname" . }}
  namespace: {{ include "prometheus-windows-exporter.namespace" . }}
  labels:
    {{- include "prometheus-windows-exporter.labels" $ | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      {{- if ( and (eq .Values.service.type "NodePort" ) (not (empty .Values.service.nodePort)) ) }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
      targetPort: {{ .Values.service.portName }}
      protocol: TCP
      appProtocol: http
      name: {{ .Values.service.portName }}
  selector:
    {{- include "prometheus-windows-exporter.selectorLabels" . | nindent 4 }}
