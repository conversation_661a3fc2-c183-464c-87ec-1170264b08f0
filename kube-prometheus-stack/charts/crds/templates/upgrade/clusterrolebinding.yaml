{{- if .Values.upgradeJob.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "kube-prometheus-stack.crd.upgradeJob.name" . }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade,pre-rollback
    "helm.sh/hook-weight": "-3"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
  labels:
    {{- include "kube-prometheus-stack.crd.upgradeJob.labels" . | nindent 4 }}
subjects:
  - kind: ServiceAccount
    namespace: {{ template "kube-prometheus-stack.namespace" . }}
    name: {{ template "kube-prometheus-stack.crd.upgradeJob.serviceAccountName" . }}
roleRef:
  kind: ClusterRole
  name: {{ template "kube-prometheus-stack.crd.upgradeJob.name" . }}
  apiGroup: rbac.authorization.k8s.io
{{- end }}
