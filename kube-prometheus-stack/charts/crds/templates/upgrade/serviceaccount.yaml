{{- if and .Values.upgradeJob.enabled .Values.upgradeJob.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.upgradeJob.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ include "kube-prometheus-stack.crd.upgradeJob.serviceAccountName" . }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade,pre-rollback
    "helm.sh/hook-weight": "-4"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
    {{- with .Values.upgradeJob.serviceAccount.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  labels:
    {{- include "kube-prometheus-stack.crd.upgradeJob.labels" . | nindent 4 }}
    {{- with .Values.upgradeJob.serviceAccount.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
