{{- if and .Values.rbac.create .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "prometheus-node-exporter.serviceAccountName" . }}
  namespace: {{ include "prometheus-node-exporter.namespace" . }}
  labels: 
    {{- include "prometheus-node-exporter.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: {{ .Values.serviceAccount.automountServiceAccountToken }}
{{- if or .Values.serviceAccount.imagePullSecrets .Values.global.imagePullSecrets }}
imagePullSecrets:
  {{- include "prometheus-node-exporter.imagePullSecrets" (dict "Values" .Values "imagePullSecrets" .Values.serviceAccount.imagePullSecrets) | indent 2 }}
{{- end }}
{{- end -}}
