{{- if and (not .Values.useStatefulSet) .Values.persistence.enabled (not .Values.persistence.existingClaim) (eq .Values.persistence.type "pvc")}}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "grafana.fullname" . }}
  namespace: {{ include "grafana.namespace" . }}
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
    {{- with .Values.persistence.extraPvcLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.persistence.annotations  }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.persistence.finalizers  }}
  finalizers:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  accessModes:
    {{- range .Values.persistence.accessModes }}
    - {{ . | quote }}
    {{- end }}
  resources:
    requests:
      storage: {{ .Values.persistence.size | quote }}
  {{- if and (.Values.persistence.lookupVolumeName) (lookup "v1" "PersistentVolumeClaim" (include "grafana.namespace" .) (include "grafana.fullname" .)) }}
  volumeName: {{ (lookup "v1" "PersistentVolumeClaim" (include "grafana.namespace" .) (include "grafana.fullname" .)).spec.volumeName }}
  {{- end }}
  {{- with .Values.persistence.storageClassName }}
  storageClassName: {{ . }}
  {{- end }}
  {{- with .Values.persistence.selectorLabels }}
  selector:
    matchLabels:
    {{- toYaml . | nindent 6 }}
  {{- end }}
{{- end }}
