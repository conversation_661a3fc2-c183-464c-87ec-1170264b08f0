{{- if and .Values.rbac.create (or (not .Values.rbac.namespaced) .Values.rbac.extraClusterRoleRules) }}
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "grafana.fullname" . }}-clusterrolebinding
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
subjects:
  - kind: ServiceAccount
    name: {{ include "grafana.serviceAccountName" . }}
    namespace: {{ include "grafana.namespace" . }}
roleRef:
  kind: ClusterRole
  {{- if .Values.rbac.useExistingClusterRole }}
  name: {{ .Values.rbac.useExistingClusterRole }}
  {{- else }}
  name: {{ include "grafana.fullname" . }}-clusterrole
  {{- end }}
  apiGroup: rbac.authorization.k8s.io
{{- end }}
