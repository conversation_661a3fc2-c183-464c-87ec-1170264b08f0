{{- if and .Values.imageRenderer.enabled .Values.imageRenderer.autoscaling.enabled }}
apiVersion: {{ include "grafana.hpa.apiVersion" . }}
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "grafana.fullname" . }}-image-renderer
  namespace: {{ include "grafana.namespace" . }}
  labels:
    app.kubernetes.io/name: {{ include "grafana.name" . }}-image-renderer
    helm.sh/chart: {{ include "grafana.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "grafana.fullname" . }}-image-renderer
  minReplicas: {{ .Values.imageRenderer.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.imageRenderer.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.imageRenderer.autoscaling.targetMemory }}
    - type: Resource
      resource:
        name: memory
        {{- if eq (include "grafana.hpa.apiVersion" .) "autoscaling/v2beta1" }}
        targetAverageUtilization: {{ .Values.imageRenderer.autoscaling.targetMemory }}
        {{- else }}
        target:
          type: Utilization
          averageUtilization: {{ .Values.imageRenderer.autoscaling.targetMemory }}
        {{- end }}
    {{- end }}
    {{- if .Values.imageRenderer.autoscaling.targetCPU }}
    - type: Resource
      resource:
        name: cpu
        {{- if eq (include "grafana.hpa.apiVersion" .) "autoscaling/v2beta1" }}
        targetAverageUtilization: {{ .Values.imageRenderer.autoscaling.targetCPU }}
        {{- else }}
        target:
          type: Utilization
          averageUtilization: {{ .Values.imageRenderer.autoscaling.targetCPU }}
        {{- end }}
    {{- end }}
  {{- if .Values.imageRenderer.autoscaling.behavior }}
  behavior: {{ toYaml .Values.imageRenderer.autoscaling.behavior | nindent 4 }}
  {{- end }}
{{- end }}
