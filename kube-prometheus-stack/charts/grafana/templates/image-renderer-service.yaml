{{- if and .Values.imageRenderer.enabled .Values.imageRenderer.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "grafana.fullname" . }}-image-renderer
  namespace: {{ include "grafana.namespace" . }}
  labels:
    {{- include "grafana.imageRenderer.labels" . | nindent 4 }}
    {{- with .Values.imageRenderer.service.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.imageRenderer.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: ClusterIP
  {{- with .Values.imageRenderer.service.clusterIP }}
  clusterIP: {{ . }}
  {{- end }}
  ports:
    - name: {{ .Values.imageRenderer.service.portName }}
      port: {{ .Values.imageRenderer.service.port }}
      protocol: TCP
      targetPort: {{ .Values.imageRenderer.service.targetPort }}
      {{- with .Values.imageRenderer.appProtocol }}
      appProtocol: {{ . }}
      {{- end }}
  selector:
    {{- include "grafana.imageRenderer.selectorLabels" . | nindent 4 }}
{{- end }}
