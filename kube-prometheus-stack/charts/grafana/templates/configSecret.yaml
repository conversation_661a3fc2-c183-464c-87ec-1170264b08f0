{{- $createConfigSecret := eq (include "grafana.shouldCreateConfigSecret" .) "true" -}}
{{- if and .Values.createConfigmap $createConfigSecret }}
{{- $files := .Files }}
{{- $root := . -}}
apiVersion: v1
kind: Secret
metadata:
  name: "{{ include "grafana.fullname" . }}-config-secret"
  namespace: {{ include "grafana.namespace" . }}
  labels:
      {{- include "grafana.labels" . | nindent 4 }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
data:
{{- range $key, $value := .Values.alerting }}
  {{- if (hasKey $value "secretFile") }}
  {{- $key | nindent 2 }}:
    {{- toYaml ( $files.Get $value.secretFile ) | b64enc | nindent 4}}
  {{/* as of https://helm.sh/docs/chart_template_guide/accessing_files/ this will only work if you fork this chart and add files to it*/}}
  {{- end }}
{{- end }}
stringData:
{{- range $key, $value := .Values.datasources }}
{{- if (hasKey $value "secret") }}
{{- $key | nindent 2 }}: |
  {{- tpl (toYaml $value.secret | nindent 4) $root }}
{{- end }}
{{- end }}
{{- range $key, $value := .Values.notifiers }}
{{- if (hasKey $value "secret") }}
{{- $key | nindent 2 }}: |
  {{- tpl (toYaml $value.secret | nindent 4) $root }}
{{- end }}
{{- end }}
{{- range $key, $value := .Values.alerting }}
{{ if (hasKey $value "secret") }}
  {{- $key | nindent 2 }}: |
    {{- tpl (toYaml $value.secret | nindent 4) $root }}
  {{- end }}
{{- end }}
{{- end }}
