{{- if and .Values.testFramework.enabled .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
  name: {{ include "grafana.serviceAccountNameTest" . }}
  namespace: {{ include "grafana.namespace" . }}
  annotations:
    "helm.sh/hook": {{ .Values.testFramework.hookType | default "test" }}
    "helm.sh/hook-delete-policy": "before-hook-creation,hook-succeeded"
{{- end }}
