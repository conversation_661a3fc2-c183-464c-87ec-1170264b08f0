{{- if .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.serviceAccount.autoMount | default .Values.serviceAccount.automountServiceAccountToken }}
metadata:
  labels:
    {{- include "grafana.labels" . | nindent 4 }}
    {{- with .Values.serviceAccount.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- tpl (toYaml . | nindent 4) $ }}
  {{- end }}
  name: {{ include "grafana.serviceAccountName" . }}
  namespace: {{ include "grafana.namespace" . }}
{{- end }}
