affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - podAffinityTerm:
          labelSelector:
            matchLabels:
              app.kubernetes.io/instance: grafana-test
              app.kubernetes.io/name: grafana
          topologyKey: failure-domain.beta.kubernetes.io/zone
        weight: 100
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            app.kubernetes.io/instance: grafana-test
            app.kubernetes.io/name: grafana
        topologyKey: kubernetes.io/hostname
