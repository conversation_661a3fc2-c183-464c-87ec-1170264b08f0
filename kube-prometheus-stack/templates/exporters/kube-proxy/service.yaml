{{- if and .Values.kubeProxy.enabled .Values.kubeProxy.service.enabled .Values.kubernetesServiceMonitors.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kube-proxy
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kube-proxy
    jobLabel: kube-proxy
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
  namespace: kube-system
spec:
  clusterIP: None
  {{- if .Values.kubeProxy.service.ipDualStack.enabled }}
  ipFamilies: {{ toYaml .Values.kubeProxy.service.ipDualStack.ipFamilies | nindent 4 }}
  ipFamilyPolicy: {{ .Values.kubeProxy.service.ipDualStack.ipFamilyPolicy }}
  {{- end }}
  ports:
    - name: {{ .Values.kubeProxy.serviceMonitor.port }}
      port: {{ .Values.kubeProxy.service.port }}
      protocol: TCP
      targetPort: {{ .Values.kubeProxy.service.targetPort }}
{{- if .Values.kubeProxy.endpoints }}{{- else }}
  selector:
    {{- if .Values.kubeProxy.service.selector }}
{{ toYaml .Values.kubeProxy.service.selector | indent 4 }}
    {{- else}}
    k8s-app: kube-proxy
    {{- end}}
{{- end }}
  type: ClusterIP
{{- end -}}
