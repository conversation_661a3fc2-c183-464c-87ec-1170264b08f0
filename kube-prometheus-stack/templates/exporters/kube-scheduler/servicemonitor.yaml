{{- if and .Values.kubeScheduler.enabled .Values.kubeScheduler.serviceMonitor.enabled .Values.kubernetesServiceMonitors.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kube-scheduler
  {{- if .Values.prometheus.prometheusSpec.ignoreNamespaceSelectors }}
  namespace: kube-system
  {{- else }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  {{- end }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kube-scheduler
  {{- with .Values.kubeScheduler.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
spec:
  jobLabel: {{ .Values.kubeScheduler.serviceMonitor.jobLabel }}
  {{- with .Values.kubeScheduler.serviceMonitor.targetLabels }}
  targetLabels:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- include "servicemonitor.scrapeLimits" .Values.kubeScheduler.serviceMonitor | nindent 2 }}
  selector:
    {{- if .Values.kubeScheduler.serviceMonitor.selector }}
    {{ tpl (toYaml .Values.kubeScheduler.serviceMonitor.selector | nindent 4) . }}
    {{- else }}
    matchLabels:
      app: {{ template "kube-prometheus-stack.name" . }}-kube-scheduler
      release: {{ $.Release.Name | quote }}
    {{- end }}
  namespaceSelector:
    matchNames:
      - "kube-system"
  endpoints:
  - port: {{ .Values.kubeScheduler.serviceMonitor.port }}
    {{- if .Values.kubeScheduler.serviceMonitor.interval }}
    interval: {{ .Values.kubeScheduler.serviceMonitor.interval }}
    {{- end }}
    bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
    {{- if .Values.kubeScheduler.serviceMonitor.proxyUrl }}
    proxyUrl: {{ .Values.kubeScheduler.serviceMonitor.proxyUrl}}
    {{- end }}
    {{- if eq (include "kube-prometheus-stack.kubeScheduler.insecureScrape" (list . false true .Values.kubeScheduler.serviceMonitor.https )) "true" }}
    scheme: https
    tlsConfig:
      caFile: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      {{- if eq (include "kube-prometheus-stack.kubeScheduler.insecureScrape" (list . nil true .Values.kubeScheduler.serviceMonitor.insecureSkipVerify)) "true" }}
      insecureSkipVerify: true
      {{- end }}
      {{- if .Values.kubeScheduler.serviceMonitor.serverName }}
      serverName: {{ .Values.kubeScheduler.serviceMonitor.serverName }}
      {{- end}}
    {{- end}}
{{- if .Values.kubeScheduler.serviceMonitor.metricRelabelings }}
    metricRelabelings:
{{ tpl (toYaml .Values.kubeScheduler.serviceMonitor.metricRelabelings | indent 4) . }}
{{- end }}
{{- if .Values.kubeScheduler.serviceMonitor.relabelings }}
    relabelings:
{{ tpl (toYaml .Values.kubeScheduler.serviceMonitor.relabelings | indent 4) . }}
{{- end }}
{{- end }}
