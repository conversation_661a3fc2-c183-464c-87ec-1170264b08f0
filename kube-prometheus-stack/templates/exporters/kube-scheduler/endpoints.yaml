{{- if and .Values.kubeScheduler.enabled .Values.kubeScheduler.endpoints .Values.kubernetesServiceMonitors.enabled }}
apiVersion: v1
kind: Endpoints
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kube-scheduler
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kube-scheduler
    k8s-app: kube-scheduler
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
  namespace: kube-system
subsets:
  - addresses:
      {{- range .Values.kubeScheduler.endpoints }}
      - ip: {{ . }}
      {{- end }}
    ports:
      - name: {{ .Values.kubeScheduler.serviceMonitor.port }}
        {{- $kubeSchedulerDefaultInsecurePort := 10251 }}
        {{- $kubeSchedulerDefaultSecurePort := 10259 }}
        port: {{ include "kube-prometheus-stack.kubeScheduler.insecureScrape" (list . $kubeSchedulerDefaultInsecurePort $kubeSchedulerDefaultSecurePort .Values.kubeScheduler.service.port)  }}
        protocol: TCP
{{- end }}
