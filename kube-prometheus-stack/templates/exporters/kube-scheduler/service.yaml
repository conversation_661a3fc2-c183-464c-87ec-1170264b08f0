{{- if and .Values.kubeScheduler.enabled .Values.kubeScheduler.service.enabled .Values.kubernetesServiceMonitors.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kube-scheduler
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kube-scheduler
    jobLabel: kube-scheduler
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
  namespace: kube-system
spec:
  clusterIP: None
  {{- if .Values.kubeScheduler.service.ipDualStack.enabled }}
  ipFamilies: {{ toYaml .Values.kubeScheduler.service.ipDualStack.ipFamilies | nindent 4 }}
  ipFamilyPolicy: {{ .Values.kubeScheduler.service.ipDualStack.ipFamilyPolicy }}
  {{- end }}
  ports:
    - name: {{ .Values.kubeScheduler.serviceMonitor.port }}
      {{- $kubeSchedulerDefaultInsecurePort := 10251 }}
      {{- $kubeSchedulerDefaultSecurePort := 10259 }}
      port: {{ include "kube-prometheus-stack.kubeScheduler.insecureScrape" (list . $kubeSchedulerDefaultInsecurePort $kubeSchedulerDefaultSecurePort .Values.kubeScheduler.service.port)  }}
      protocol: TCP
      targetPort: {{ include "kube-prometheus-stack.kubeScheduler.insecureScrape" (list . $kubeSchedulerDefaultInsecurePort $kubeSchedulerDefaultSecurePort .Values.kubeScheduler.service.targetPort)  }}
{{- if .Values.kubeScheduler.endpoints }}{{- else }}
  selector:
    {{- if .Values.kubeScheduler.service.selector }}
{{ toYaml .Values.kubeScheduler.service.selector | indent 4 }}
    {{- else}}
    component: kube-scheduler
    {{- end}}
{{- end }}
  type: ClusterIP
{{- end -}}
