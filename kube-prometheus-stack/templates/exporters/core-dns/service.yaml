{{- if and .Values.coreDns.enabled .Values.coreDns.service.enabled .Values.kubernetesServiceMonitors.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-coredns
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-coredns
    jobLabel: coredns
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
  namespace: kube-system
spec:
  clusterIP: None
  {{- if .Values.coreDns.service.ipDualStack.enabled }}
  ipFamilies: {{ toYaml .Values.coreDns.service.ipDualStack.ipFamilies | nindent 4 }}
  ipFamilyPolicy: {{ .Values.coreDns.service.ipDualStack.ipFamilyPolicy }}
  {{- end }}
  ports:
    - name: {{ .Values.coreDns.serviceMonitor.port }}
      port: {{ .Values.coreDns.service.port }}
      protocol: TCP
      targetPort: {{ .Values.coreDns.service.targetPort }}
  selector:
    {{- if .Values.coreDns.service.selector }}
{{ toYaml .Values.coreDns.service.selector | indent 4 }}
    {{- else}}
    k8s-app: kube-dns
    {{- end}}
{{- end }}
