{{- if and .Values.kubeControllerManager.enabled .Values.kubeControllerManager.endpoints .Values.kubernetesServiceMonitors.enabled }}
apiVersion: v1
kind: Endpoints
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kube-controller-manager
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kube-controller-manager
    k8s-app: kube-controller-manager
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
  namespace: kube-system
subsets:
  - addresses:
      {{- range .Values.kubeControllerManager.endpoints }}
      - ip: {{ . }}
      {{- end }}
    ports:
      - name: {{ .Values.kubeControllerManager.serviceMonitor.port }}
        {{- $kubeControllerManagerDefaultInsecurePort := 10252 }}
        {{- $kubeControllerManagerDefaultSecurePort := 10257 }}
        port: {{ include "kube-prometheus-stack.kubeControllerManager.insecureScrape" (list . $kubeControllerManagerDefaultInsecurePort $kubeControllerManagerDefaultSecurePort .Values.kubeControllerManager.service.port) }}
        protocol: TCP
{{- end }}
