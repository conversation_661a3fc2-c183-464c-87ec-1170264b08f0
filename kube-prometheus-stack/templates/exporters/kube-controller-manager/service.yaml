{{- if and .Values.kubeControllerManager.enabled .Values.kubeControllerManager.service.enabled .Values.kubernetesServiceMonitors.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kube-controller-manager
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kube-controller-manager
    jobLabel: kube-controller-manager
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
  namespace: kube-system
spec:
  clusterIP: None
  {{- if .Values.kubeControllerManager.service.ipDualStack.enabled }}
  ipFamilies: {{ toYaml .Values.kubeControllerManager.service.ipDualStack.ipFamilies | nindent 4 }}
  ipFamilyPolicy: {{ .Values.kubeControllerManager.service.ipDualStack.ipFamilyPolicy }}
  {{- end }}
  ports:
    - name: {{ .Values.kubeControllerManager.serviceMonitor.port }}
      {{- $kubeControllerManagerDefaultInsecurePort := 10252 }}
      {{- $kubeControllerManagerDefaultSecurePort := 10257 }}
      port: {{ include "kube-prometheus-stack.kubeControllerManager.insecureScrape" (list . $kubeControllerManagerDefaultInsecurePort $kubeControllerManagerDefaultSecurePort .Values.kubeControllerManager.service.port) }}
      protocol: TCP
      targetPort: {{ include "kube-prometheus-stack.kubeControllerManager.insecureScrape" (list . $kubeControllerManagerDefaultInsecurePort $kubeControllerManagerDefaultSecurePort .Values.kubeControllerManager.service.targetPort) }}
{{- if .Values.kubeControllerManager.endpoints }}{{- else }}
  selector:
    {{- if .Values.kubeControllerManager.service.selector }}
{{ toYaml .Values.kubeControllerManager.service.selector | indent 4 }}
    {{- else}}
    component: kube-controller-manager
    {{- end}}
{{- end }}
  type: ClusterIP
{{- end }}
