{{- if and .Values.kubeEtcd.enabled .Values.kubeEtcd.service.enabled .Values.kubernetesServiceMonitors.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kube-etcd
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kube-etcd
    jobLabel: kube-etcd
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
  namespace: kube-system
spec:
  clusterIP: None
  {{- if .Values.kubeEtcd.service.ipDualStack.enabled }}
  ipFamilies: {{ toYaml .Values.kubeEtcd.service.ipDualStack.ipFamilies | nindent 4 }}
  ipFamilyPolicy: {{ .Values.kubeEtcd.service.ipDualStack.ipFamilyPolicy }}
  {{- end }}
  ports:
    - name: {{ .Values.kubeEtcd.serviceMonitor.port }}
      port: {{ .Values.kubeEtcd.service.port }}
      protocol: TCP
      targetPort: {{ .Values.kubeEtcd.service.targetPort }}
{{- if .Values.kubeEtcd.endpoints }}{{- else }}
  selector:
    {{- if .Values.kubeEtcd.service.selector }}
{{ toYaml .Values.kubeEtcd.service.selector | indent 4 }}
    {{- else}}
    component: etcd
    {{- end}}
{{- end }}
  type: ClusterIP
{{- end -}}
