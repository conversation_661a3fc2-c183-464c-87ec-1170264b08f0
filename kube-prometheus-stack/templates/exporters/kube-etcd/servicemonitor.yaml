{{- if and .Values.kubeEtcd.enabled .Values.kubeEtcd.serviceMonitor.enabled .Values.kubernetesServiceMonitors.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kube-etcd
  {{- if .Values.prometheus.prometheusSpec.ignoreNamespaceSelectors }}
  namespace: kube-system
  {{- else }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  {{- end }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kube-etcd
  {{- with .Values.kubeEtcd.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
spec:
  jobLabel: {{ .Values.kubeEtcd.serviceMonitor.jobLabel }}
  {{- with .Values.kubeEtcd.serviceMonitor.targetLabels }}
  targetLabels:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- include "servicemonitor.scrapeLimits" .Values.kubeEtcd.serviceMonitor | nindent 4 }}
  selector:
    {{- if .Values.kubeEtcd.serviceMonitor.selector }}
    {{ tpl (toYaml .Values.kubeEtcd.serviceMonitor.selector | nindent 4) . }}
    {{- else }}
    matchLabels:
      app: {{ template "kube-prometheus-stack.name" . }}-kube-etcd
      release: {{ $.Release.Name | quote }}
    {{- end }}
  namespaceSelector:
    matchNames:
      - "kube-system"
  endpoints:
  - port: {{ .Values.kubeEtcd.serviceMonitor.port }}
    {{- if .Values.kubeEtcd.serviceMonitor.interval }}
    interval: {{ .Values.kubeEtcd.serviceMonitor.interval }}
    {{- end }}
    bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
    {{- if .Values.kubeEtcd.serviceMonitor.proxyUrl }}
    proxyUrl: {{ .Values.kubeEtcd.serviceMonitor.proxyUrl}}
    {{- end }}
    {{- if eq .Values.kubeEtcd.serviceMonitor.scheme "https" }}
    scheme: https
    tlsConfig:
      {{- if .Values.kubeEtcd.serviceMonitor.serverName }}
      serverName: {{ .Values.kubeEtcd.serviceMonitor.serverName }}
      {{- end }}
      {{- if .Values.kubeEtcd.serviceMonitor.caFile }}
      caFile: {{ .Values.kubeEtcd.serviceMonitor.caFile }}
      {{- end }}
      {{- if  .Values.kubeEtcd.serviceMonitor.certFile }}
      certFile: {{ .Values.kubeEtcd.serviceMonitor.certFile }}
      {{- end }}
      {{- if .Values.kubeEtcd.serviceMonitor.keyFile }}
      keyFile: {{ .Values.kubeEtcd.serviceMonitor.keyFile }}
      {{- end}}
      insecureSkipVerify: {{ .Values.kubeEtcd.serviceMonitor.insecureSkipVerify }}
    {{- end }}
{{- if .Values.kubeEtcd.serviceMonitor.metricRelabelings }}
    metricRelabelings:
{{ tpl (toYaml .Values.kubeEtcd.serviceMonitor.metricRelabelings | indent 4) . }}
{{- end }}
{{- if .Values.kubeEtcd.serviceMonitor.relabelings }}
    relabelings:
{{ tpl (toYaml .Values.kubeEtcd.serviceMonitor.relabelings | indent 4) . }}
{{- end }}
{{- end }}
