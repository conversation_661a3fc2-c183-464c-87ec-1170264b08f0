{{- if and .Values.kubeDns.enabled .Values.kubernetesServiceMonitors.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-kube-dns
  {{- if .Values.prometheus.prometheusSpec.ignoreNamespaceSelectors }}
  namespace: kube-system
  {{- else }}
  namespace: {{ template "kube-prometheus-stack.namespace" . }}
  {{- end }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-kube-dns
  {{- with .Values.kubeDns.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
spec:
  jobLabel: {{ .Values.kubeDns.serviceMonitor.jobLabel }}
  {{- with .Values.kubeDns.serviceMonitor.targetLabels }}
  targetLabels:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- include "servicemonitor.scrapeLimits" .Values.kubeDns.serviceMonitor | nindent 2 }}
  selector:
    {{- if .Values.kubeDns.serviceMonitor.selector }}
    {{ tpl (toYaml .Values.kubeDns.serviceMonitor.selector | nindent 4) . }}
    {{- else }}
    matchLabels:
      app: {{ template "kube-prometheus-stack.name" . }}-kube-dns
      release: {{ $.Release.Name | quote }}
    {{- end }}
  namespaceSelector:
    matchNames:
      - "kube-system"
  endpoints:
  - port: http-metrics-dnsmasq
    {{- if .Values.kubeDns.serviceMonitor.interval }}
    interval: {{ .Values.kubeDns.serviceMonitor.interval }}
    {{- end }}
    bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
    {{- if .Values.kubeDns.serviceMonitor.proxyUrl }}
    proxyUrl: {{ .Values.kubeDns.serviceMonitor.proxyUrl}}
    {{- end }}
{{- if .Values.kubeDns.serviceMonitor.dnsmasqMetricRelabelings }}
    metricRelabelings:
{{ tpl (toYaml .Values.kubeDns.serviceMonitor.dnsmasqMetricRelabelings | indent 4) . }}
{{- end }}
{{- if .Values.kubeDns.serviceMonitor.dnsmasqRelabelings }}
    relabelings:
{{ toYaml .Values.kubeDns.serviceMonitor.dnsmasqRelabelings | indent 4 }}
{{- end }}
  - port: http-metrics-skydns
    {{- if .Values.kubeDns.serviceMonitor.interval }}
    interval: {{ .Values.kubeDns.serviceMonitor.interval }}
    {{- end }}
    bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/token
{{- if .Values.kubeDns.serviceMonitor.metricRelabelings }}
    metricRelabelings:
{{ tpl (toYaml .Values.kubeDns.serviceMonitor.metricRelabelings | indent 4) . }}
{{- end }}
{{- if .Values.kubeDns.serviceMonitor.relabelings }}
    relabelings:
{{ tpl (toYaml .Values.kubeDns.serviceMonitor.relabelings | indent 4) . }}
{{- end }}
{{- end }}
