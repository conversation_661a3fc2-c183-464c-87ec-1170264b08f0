{{- if or (and .Values.grafana.enabled .Values.grafana.sidecar.datasources.enabled) .Values.grafana.forceDeployDatasources }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-grafana-datasource
  namespace: {{ template "kube-prometheus-stack-grafana.namespace" . }}
{{- if .Values.grafana.sidecar.datasources.annotations }}
  annotations:
    {{- toYaml .Values.grafana.sidecar.datasources.annotations | nindent 4 }}
{{- end }}
  labels:
    {{ $.Values.grafana.sidecar.datasources.label }}: {{ $.Values.grafana.sidecar.datasources.labelValue | quote }}
    app: {{ template "kube-prometheus-stack.name" $ }}-grafana
{{ include "kube-prometheus-stack.labels" $ | indent 4 }}
data:
  datasource.yaml: |-
    apiVersion: 1
{{- if .Values.grafana.deleteDatasources }}
    deleteDatasources:
{{ tpl (toYaml .Values.grafana.deleteDatasources | indent 6) . }}
{{- end }}
{{- if .Values.grafana.prune }}
    prune: {{ .Values.grafana.prune }}
{{- end }}
    datasources:
{{- $scrapeInterval := .Values.grafana.sidecar.datasources.defaultDatasourceScrapeInterval | default .Values.prometheus.prometheusSpec.scrapeInterval | default "30s" }}
{{- if .Values.grafana.sidecar.datasources.defaultDatasourceEnabled }}
    - name: "{{ .Values.grafana.sidecar.datasources.name }}"
      type: prometheus
      uid: {{ .Values.grafana.sidecar.datasources.uid }}
      {{- if .Values.grafana.sidecar.datasources.url }}
      url: {{ .Values.grafana.sidecar.datasources.url }}
      {{- else }}
      url: http://{{ template "kube-prometheus-stack.fullname" . }}-prometheus.{{ template "kube-prometheus-stack.namespace" . }}:{{ .Values.prometheus.service.port }}/{{ trimPrefix "/" .Values.prometheus.prometheusSpec.routePrefix }}
      {{- end }}
      access: proxy
      isDefault: {{ .Values.grafana.sidecar.datasources.isDefaultDatasource }}
      jsonData:
        httpMethod: {{ .Values.grafana.sidecar.datasources.httpMethod }}
        timeInterval: {{ $scrapeInterval }}
        {{- if .Values.grafana.sidecar.datasources.timeout }}
        timeout: {{ .Values.grafana.sidecar.datasources.timeout }}
        {{- end }}
{{- if .Values.grafana.sidecar.datasources.exemplarTraceIdDestinations }}
        exemplarTraceIdDestinations:
        - datasourceUid: {{ .Values.grafana.sidecar.datasources.exemplarTraceIdDestinations.datasourceUid }}
          name: {{ .Values.grafana.sidecar.datasources.exemplarTraceIdDestinations.traceIdLabelName }}
          urlDisplayLabel: {{ .Values.grafana.sidecar.datasources.exemplarTraceIdDestinations.urlDisplayLabel }}
{{- end }}
{{- if .Values.grafana.sidecar.datasources.createPrometheusReplicasDatasources }}
{{- range until (int .Values.prometheus.prometheusSpec.replicas) }}
    - name: "{{ $.Values.grafana.sidecar.datasources.name }}-{{ . }}"
      type: prometheus
      uid: {{ $.Values.grafana.sidecar.datasources.uid }}-replica-{{ . }}
      url: http://prometheus-{{ template "kube-prometheus-stack.prometheus.crname" $ }}-{{ . }}.{{ $.Values.grafana.sidecar.datasources.prometheusServiceName}}:9090/{{ trimPrefix "/" $.Values.prometheus.prometheusSpec.routePrefix }}
      access: proxy
      isDefault: false
      jsonData:
        timeInterval: {{ $scrapeInterval }}
{{- if $.Values.grafana.sidecar.datasources.exemplarTraceIdDestinations }}
        exemplarTraceIdDestinations:
        - datasourceUid: {{ $.Values.grafana.sidecar.datasources.exemplarTraceIdDestinations.datasourceUid }}
          name: {{ $.Values.grafana.sidecar.datasources.exemplarTraceIdDestinations.traceIdLabelName }}
          urlDisplayLabel: {{ .Values.grafana.sidecar.datasources.exemplarTraceIdDestinations.urlDisplayLabel }}
{{- end }}
{{- end }}
{{- end }}
{{- if .Values.grafana.sidecar.datasources.alertmanager.enabled }}
    - name: "{{ .Values.grafana.sidecar.datasources.alertmanager.name }}"
      type: alertmanager
      uid: {{ .Values.grafana.sidecar.datasources.alertmanager.uid }}
      {{- if .Values.grafana.sidecar.datasources.alertmanager.url }}
      url: {{ .Values.grafana.sidecar.datasources.alertmanager.url }}
      {{- else }}
      url: http://{{ template "kube-prometheus-stack.fullname" . }}-alertmanager.{{ template "kube-prometheus-stack.namespace" . }}:{{ .Values.alertmanager.service.port }}/{{ trimPrefix "/" .Values.alertmanager.alertmanagerSpec.routePrefix }}
      {{- end }}
      access: proxy
      jsonData:
        handleGrafanaManagedAlerts: {{ .Values.grafana.sidecar.datasources.alertmanager.handleGrafanaManagedAlerts }}
        implementation: {{ .Values.grafana.sidecar.datasources.alertmanager.implementation }}
{{- end }}
{{- end }}
{{- if .Values.grafana.additionalDataSources }}
{{ tpl (toYaml .Values.grafana.additionalDataSources | indent 4) . }}
{{- end }}
{{- end }}
