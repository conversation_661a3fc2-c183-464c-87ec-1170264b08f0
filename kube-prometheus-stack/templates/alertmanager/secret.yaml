{{- if and (.Values.alertmanager.enabled) (not .Values.alertmanager.alertmanagerSpec.useExistingSecret) }}
apiVersion: v1
kind: Secret
metadata:
  name: alertmanager-{{ template "kube-prometheus-stack.alertmanager.crname" . }}
  namespace: {{ template "kube-prometheus-stack-alertmanager.namespace" . }}
{{- if .Values.alertmanager.secret.annotations }}
  annotations:
{{ toYaml .Values.alertmanager.secret.annotations | indent 4 }}
{{- end }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-alertmanager
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
data:
{{- if .Values.alertmanager.tplConfig }}
{{- if .Values.alertmanager.stringConfig }}
  alertmanager.yaml: {{ tpl (.Values.alertmanager.stringConfig) . | b64enc | quote }}
{{- else if eq (typeOf .Values.alertmanager.config) "string" }}
  alertmanager.yaml: {{ tpl (.Values.alertmanager.config) . | b64enc | quote }}
{{- else }}
  alertmanager.yaml: {{ tpl (toYaml .Values.alertmanager.config) . | b64enc | quote }}
{{- end }}
{{- else }}
  alertmanager.yaml: {{ toYaml .Values.alertmanager.config | b64enc | quote }}
{{- end }}
{{- range $key, $val := .Values.alertmanager.templateFiles }}
  {{ $key }}: {{ $val | b64enc | quote }}
{{- end }}
{{- end }}
