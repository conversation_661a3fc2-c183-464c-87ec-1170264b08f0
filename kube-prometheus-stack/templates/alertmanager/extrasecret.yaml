{{- if .Values.alertmanager.extraSecret.data -}}
{{- $secretName := printf "alertmanager-%s-extra" (include "kube-prometheus-stack.fullname" . ) -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ default $secretName .Values.alertmanager.extraSecret.name }}
  namespace: {{ template "kube-prometheus-stack-alertmanager.namespace" . }}
{{- if .Values.alertmanager.extraSecret.annotations }}
  annotations:
{{ toYaml .Values.alertmanager.extraSecret.annotations | indent 4 }}
{{- end }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-alertmanager
    app.kubernetes.io/component: alertmanager
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
data:
{{- range $key, $val := .Values.alertmanager.extraSecret.data }}
  {{ $key }}: {{ $val | b64enc | quote }}
{{- end }}
{{- end }}
