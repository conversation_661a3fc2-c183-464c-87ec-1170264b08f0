{{- if and .Values.alertmanager.enabled .Values.global.rbac.create .Values.global.rbac.pspEnabled }}
{{- if .Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy" }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-alertmanager
  namespace: {{ template "kube-prometheus-stack-alertmanager.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-alertmanager
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "kube-prometheus-stack.fullname" . }}-alertmanager
subjects:
  - kind: ServiceAccount
    name: {{ template "kube-prometheus-stack.alertmanager.serviceAccountName" . }}
    namespace: {{ template "kube-prometheus-stack-alertmanager.namespace" . }}
{{- end }}
{{- end }}
