{{- if and .Values.alertmanager.enabled .Values.alertmanager.podDisruptionBudget.enabled }}
apiVersion: {{ include "kube-prometheus-stack.pdb.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-alertmanager
  namespace: {{ template "kube-prometheus-stack-alertmanager.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-alertmanager
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
spec:
  {{- if .Values.alertmanager.podDisruptionBudget.minAvailable }}
  minAvailable: {{ .Values.alertmanager.podDisruptionBudget.minAvailable }}
  {{- end }}
  {{- if .Values.alertmanager.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ .Values.alertmanager.podDisruptionBudget.maxUnavailable }}
  {{- end }}
  selector:
    matchLabels:
      app.kubernetes.io/name: alertmanager
      alertmanager: {{ template "kube-prometheus-stack.alertmanager.crname" . }}
{{- end }}
