{{- $kubeTargetVersion := default .Capabilities.KubeVersion.GitVersion .Values.kubeTargetVersionOverride }}
{{- if and .Values.alertmanager.enabled .Values.alertmanager.service.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-alertmanager
  namespace: {{ template "kube-prometheus-stack-alertmanager.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-alertmanager
    self-monitor: {{ .Values.alertmanager.serviceMonitor.selfMonitor | quote }}
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
{{- if .Values.alertmanager.service.labels }}
{{ toYaml .Values.alertmanager.service.labels | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.service.annotations }}
  annotations:
{{ toYaml .Values.alertmanager.service.annotations | indent 4 }}
{{- end }}
spec:
{{- if .Values.alertmanager.service.clusterIP }}
  clusterIP: {{ .Values.alertmanager.service.clusterIP }}
{{- end }}
{{- if .Values.alertmanager.service.externalIPs }}
  externalIPs:
{{ toYaml .Values.alertmanager.service.externalIPs | indent 4 }}
{{- end }}
{{- if .Values.alertmanager.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.alertmanager.service.loadBalancerIP }}
{{- end }}
{{- if .Values.alertmanager.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
  {{- range $cidr := .Values.alertmanager.service.loadBalancerSourceRanges }}
    - {{ $cidr }}
  {{- end }}
{{- end }}
{{- if ne .Values.alertmanager.service.type "ClusterIP" }}
  externalTrafficPolicy: {{ .Values.alertmanager.service.externalTrafficPolicy }}
{{- end }}
  ports:
  - name: {{ .Values.alertmanager.alertmanagerSpec.portName }}
  {{- if eq .Values.alertmanager.service.type "NodePort" }}
    nodePort: {{ .Values.alertmanager.service.nodePort }}
  {{- end }}
    port: {{ .Values.alertmanager.service.port }}
    targetPort: {{ .Values.alertmanager.service.targetPort }}
    protocol: TCP
  - name: reloader-web
    {{- if semverCompare ">=1.20.0-0" $kubeTargetVersion }}
    appProtocol: http
    {{- end }}
    port: 8080
    targetPort: reloader-web
{{- if .Values.alertmanager.service.additionalPorts }}
{{ toYaml .Values.alertmanager.service.additionalPorts | indent 2 }}
{{- end }}
  selector:
    app.kubernetes.io/name: alertmanager
    alertmanager: {{ template "kube-prometheus-stack.alertmanager.crname" . }}
{{- if .Values.alertmanager.service.sessionAffinity }}
  sessionAffinity: {{ .Values.alertmanager.service.sessionAffinity }}
{{- end }}
{{- if eq .Values.alertmanager.service.sessionAffinity "ClientIP" }}
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: {{ .Values.alertmanager.service.sessionAffinityConfig.clientIP.timeoutSeconds }}
{{- end }}
  type: "{{ .Values.alertmanager.service.type }}"
{{- if .Values.alertmanager.service.ipDualStack.enabled }}
  ipFamilies: {{ toYaml .Values.alertmanager.service.ipDualStack.ipFamilies | nindent 4 }}
  ipFamilyPolicy: {{ .Values.alertmanager.service.ipDualStack.ipFamilyPolicy }}
{{- end }}
{{- end }}