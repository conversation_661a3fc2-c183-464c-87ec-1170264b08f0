{{- if and .Values.alertmanager.enabled .Values.alertmanager.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ template "kube-prometheus-stack.alertmanager.serviceAccountName" . }}
  namespace: {{ template "kube-prometheus-stack-alertmanager.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-alertmanager
    app.kubernetes.io/name: {{ template "kube-prometheus-stack.name" . }}-alertmanager
    app.kubernetes.io/component: alertmanager
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
{{- if .Values.alertmanager.serviceAccount.annotations }}
  annotations:
{{ toYaml .Values.alertmanager.serviceAccount.annotations | indent 4 }}
{{- end }}
automountServiceAccountToken: {{ .Values.alertmanager.serviceAccount.automountServiceAccountToken }}
{{- if .Values.global.imagePullSecrets }}
imagePullSecrets:
{{ include "kube-prometheus-stack.imagePullSecrets" . | trim | indent 2}}
{{- end }}
{{- end }}
