{{- if and .Values.alertmanager.enabled .Values.global.rbac.create .Values.global.rbac.pspEnabled }}
{{- if .Capabilities.APIVersions.Has "policy/v1beta1/PodSecurityPolicy" }}
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ template "kube-prometheus-stack.fullname" . }}-alertmanager
  namespace: {{ template "kube-prometheus-stack-alertmanager.namespace" . }}
  labels:
    app: {{ template "kube-prometheus-stack.name" . }}-alertmanager
{{ include "kube-prometheus-stack.labels" . | indent 4 }}
rules:
{{- $kubeTargetVersion := default .Capabilities.KubeVersion.GitVersion .Values.kubeTargetVersionOverride }}
{{- if semverCompare "> 1.15.0-0" $kubeTargetVersion }}
- apiGroups: ['policy']
{{- else }}
- apiGroups: ['extensions']
{{- end }}
  resources: ['podsecuritypolicies']
  verbs:     ['use']
  resourceNames:
  - {{ template "kube-prometheus-stack.fullname" . }}-alertmanager
{{- end }}
{{- end }}
