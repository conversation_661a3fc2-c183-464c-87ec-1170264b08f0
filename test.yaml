apiVersion: v1
kind: Pod
metadata:
  name: imagepullbackoff-test-pod
  labels:
    test: imagepullbackoff
spec:
  containers:
    - name: nonexistent-image-container
      # Use an image name that definitely does not exist on Docker Hub or any other registry
      # You can also use an existing image with a non-existent tag, e.g., 'nginx:nonexistenttag'
      image: non-existent-registry.com/my-non-existent-image:latest
      
      # Use imagePullPolicy: Always to ensure Kubernetes always tries to pull
      # (though for a non-existent image, it will always fail anyway)
      imagePullPolicy: Always
      
      # A simple command that will run if the image were pulled,
      # but it won't get this far.
      command: ["/bin/sh", "-c", "echo 'Image pulled successfully (unlikely!)' && sleep 3600"]
  
  # Set restartPolicy to Always (default) or OnFailure to see the "BackOff" behavior
  # if it's set to Never, it will just fail once and stay failed.
  restartPolicy: Always
---
apiVersion: v1
kind: Pod
metadata:
  name: oomkilled-test-pod
  labels:
    test: oomkilled
spec:
  restartPolicy: Never # Prevents the pod from restarting after being OOMKilled
  containers:
    - name: memory-eater
      image: python:3.9-slim-bullseye # A small image with Python
      command: ["python", "-c"]
      args:
        - |
          import time
          data = []
          # Attempt to allocate 500MB (500 * 1MB strings)
          for i in range(500):
              data.append('A' * 1024 * 1024)
          print("Attempted to allocate 500MB. Sleeping now...")
          time.sleep(3600) # Keep the process alive for a while if it doesn't OOM quickly
      resources:
        limits:
          memory: "10Mi" # Set a low memory limit (200 MiB)
          cpu: "10m"
        requests:
          memory: "10Mi" # Request a bit less than the limit
          cpu: "10m"