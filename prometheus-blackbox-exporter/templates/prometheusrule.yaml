{{- if .Values.prometheusRule.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ template "prometheus-blackbox-exporter.fullname" . }}
  {{- with .Values.prometheusRule.namespace }}
  namespace: {{ . }}
  {{- end }}
  labels:
    {{- include "prometheus-blackbox-exporter.labels" . | nindent 4 }}
    {{- with .Values.prometheusRule.additionalLabels -}}
{{- toYaml . | nindent 4 -}}
    {{- end }}
spec:
  {{- with .Values.prometheusRule.rules }}
  groups:
    - name: {{ template "prometheus-blackbox-exporter.name" $ }}
      rules: {{ toYaml . | nindent 8 }}
  {{- end }}
{{- end }}
