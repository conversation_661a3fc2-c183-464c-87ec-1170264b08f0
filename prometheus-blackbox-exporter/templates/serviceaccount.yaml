{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "prometheus-blackbox-exporter.serviceAccountName" . }}
  namespace: {{ template "prometheus-blackbox-exporter.namespace" . }}
  labels:
    {{- include "prometheus-blackbox-exporter.labels" . | nindent 4 }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
