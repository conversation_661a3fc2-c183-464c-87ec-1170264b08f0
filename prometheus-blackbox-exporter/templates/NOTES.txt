See https://github.com/prometheus/blackbox_exporter/ for how to configure Prometheus and the Blackbox Exporter.

1. Get the application URL by running these commands:
{{- if .Values.ingress.enabled }}
{{- range $host := .Values.ingress.hosts }}
  {{- range .paths }}
  http{{ if $.Values.ingress.tls }}s{{ end }}://{{ $host.host }}{{ .path }}
  {{- end }}
{{- end }}

{{- $kubeVersion := include "prometheus-blackbox-exporter.kubeVersion" . -}}
{{ if and .Values.ingress.className (semverCompare "<=1.18-0" $kubeVersion) }}
You've set ".Values.ingressClassName" but it's not supported by your Kubernetes version!
Therefore the option was not added and the old ingress annotation was set.
{{ end }}

{{- else if contains "NodePort" .Values.service.type }}
  export NODE_PORT=$(kubectl get --namespace {{ template "prometheus-blackbox-exporter.namespace" . }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "prometheus-blackbox-exporter.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ template "prometheus-blackbox-exporter.namespace" . }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo http://$NODE_IP:$NODE_PORT
{{- else if contains "LoadBalancer" .Values.service.type }}
     NOTE: It may take a few minutes for the LoadBalancer IP to be available.
           You can watch the status of by running 'kubectl get --namespace {{ template "prometheus-blackbox-exporter.namespace" . }} svc -w {{ include "prometheus-blackbox-exporter.fullname" . }}'
  export SERVICE_IP=$(kubectl get svc --namespace {{ template "prometheus-blackbox-exporter.namespace" . }} {{ include "prometheus-blackbox-exporter.fullname" . }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")
  echo http://$SERVICE_IP:{{ .Values.service.port }}
{{- else if contains "ClusterIP" .Values.service.type }}
  export POD_NAME=$(kubectl get pods --namespace {{ template "prometheus-blackbox-exporter.namespace" . }} -l "app.kubernetes.io/name={{ include "prometheus-blackbox-exporter.name" . }},app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
  export CONTAINER_PORT=$(kubectl get pod --namespace {{ template "prometheus-blackbox-exporter.namespace" . }} $POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:8080 to use your application"
  kubectl --namespace {{ template "prometheus-blackbox-exporter.namespace" . }} port-forward $POD_NAME 8080:$CONTAINER_PORT
{{- end }}
