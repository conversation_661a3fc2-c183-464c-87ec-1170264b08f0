{{- if .Values.podDisruptionBudget -}}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ template "prometheus-blackbox-exporter.fullname" . }}
  namespace: {{ template "prometheus-blackbox-exporter.namespace" . }}
  labels:
    {{- include "prometheus-blackbox-exporter.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "prometheus-blackbox-exporter.selectorLabels" . | nindent 6 }}
{{ toYaml .Values.podDisruptionBudget | indent 2 }}
{{- end }}
