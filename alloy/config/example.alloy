logging {
	level  = "warn"
	format = "logfmt"
}

discovery.kubernetes "cadvisor_pods" {
  role = "node"
}

discovery.relabel "cadvisor_pods" {
  targets = discovery.kubernetes.cadvisor_pods.targets

  rule {
    target_label = "__metrics_path__"
    replacement = "/metrics/cadvisor"
  }
  rule {
    source_labels = ["__meta_kubernetes_node_name"]
    target_label  = "instance"
  }
}

prometheus.scrape "cadvisor" {
  targets = discovery.relabel.cadvisor_pods.output
  scheme = "https"
  job_name = "cadvisor"
  forward_to = [prometheus.remote_write.default.receiver]

  tls_config {
    insecure_skip_verify = true
  }

  authorization {
    type = "Bearer"
    credentials_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
  }
}


discovery.kubernetes "kube_state_metrics" {
  role = "endpoints"

  selectors {
    role      = "endpoints"
    label     = "app.kubernetes.io/name=kube-state-metrics"
  }
}

prometheus.scrape "kube_state_metrics" {
  targets        = discovery.kubernetes.kube_state_metrics.targets
  scrape_interval = "30s"
  metrics_path    = "/metrics"
  scheme          = "http"
  forward_to = [prometheus.remote_write.default.receiver]
  job_name   = "kube-state-metrics"
}

discovery.kubernetes "endpointslice" {
  role = "endpointslice"
}

discovery.relabel "node_exporter" {
  targets = discovery.kubernetes.endpointslice.targets

  rule {
    source_labels = ["__meta_kubernetes_endpointslice_port"]
    regex         = "9100"
    action        = "keep"
  }
  rule {
    source_labels = ["__meta_kubernetes_pod_name"]
    target_label  = "pod"
  }
  rule {
    source_labels = ["__meta_kubernetes_pod_node_name"]
    target_label  = "instance"
  }
}
prometheus.scrape "node_exporter" {
  targets    = discovery.relabel.node_exporter.output
  forward_to = [prometheus.remote_write.default.receiver]
  job_name   = "node_exporter"
}

prometheus.scrape "blackbox_scraper1" {
  targets = [
    {"__address__" = "*************:9115", "instance" = "kalpha-n-data-integrations"},
  ]
  forward_to = [prometheus.remote_write.default.receiver]
  scrape_interval = "10s"
  params          = { "target" = ["https://kalpha-n-data-integrations.admin.strawmine.com/admin/api/v1/status/report"], "module" = ["http_2xx"] }
  metrics_path    = "/probe"
}

prometheus.scrape "blackbox_scraper2" {
  targets = [
    {"__address__" = "*************:9115", "instance" = "kalpha-n-machine-maintenance-web-external"},
  ]
  forward_to = [prometheus.remote_write.default.receiver]
  scrape_interval = "10s"
  params          = { "target" = ["https://kalpha-n-machine-maintenance-web-external.strawmine.com"], "module" = ["http_2xx"] }
  metrics_path    = "/probe"
}


discovery.relabel "jmx_metrics" {
  targets = discovery.kubernetes.endpointslice.targets

  rule {
    source_labels = ["__meta_kubernetes_endpointslice_port"]
    regex         = "9050"
    action        = "keep"
  }
  rule {
    source_labels = ["__meta_kubernetes_pod_name"]
    target_label  = "instance"
  }
  rule {
    source_labels = ["__meta_kubernetes_pod_node_name"]
    target_label  = "node"
  }
}

prometheus.scrape "jmx_metrics" {
  targets    = discovery.relabel.jmx_metrics.output
  forward_to = [prometheus.remote_write.default.receiver]
  job_name   = "jmx"
}

discovery.relabel "ingress_nginx" {
  targets = discovery.kubernetes.endpointslice.targets

  rule {
    source_labels = ["__meta_kubernetes_endpointslice_port"]
    regex         = "10254"
    action        = "keep"
  }

  rule {
    source_labels = ["__meta_kubernetes_pod_label_app_kubernetes_io_name"]
    regex         = "ingress-nginx"
    action        = "keep"
  }

  rule {
    source_labels = ["__meta_kubernetes_namespace"]
    target_label  = "namespace"
  }

  rule {
    source_labels = ["__meta_kubernetes_pod_name"]
    target_label  = "pod"
  }

  rule {
    source_labels = ["__meta_kubernetes_pod_node_name"]
    target_label  = "instance"
  }

  rule {
    source_labels = ["__meta_kubernetes_service_name"]
    target_label  = "service"
  }
}

prometheus.scrape "ingress_nginx" {
  targets    = discovery.relabel.ingress_nginx.output
  forward_to = [prometheus.remote_write.default.receiver]
  job_name   = "ingress_nginx"
  scrape_interval = "30s"
  metrics_path = "/metrics"
}


discovery.kubernetes "kubernetes_pods" {
  role = "pod"
}

discovery.relabel "kubernetes_pods" {
  targets = discovery.kubernetes.kubernetes_pods.targets
  rule {
    source_labels = ["__meta_kubernetes_pod_node_name"]
    target_label  = "node_name"
  }
  rule {
    source_labels = ["__meta_kubernetes_namespace"]
    target_label  = "namespace"
  }
  rule {
    source_labels = ["__meta_kubernetes_pod_name"]
    target_label  = "pod"
  }
  rule {
    source_labels = ["__meta_kubernetes_pod_container_name"]
    target_label  = "container"
  }
  rule {
    source_labels = ["__meta_kubernetes_namespace"]
    regex         = "kube-system|monitoring-mimir|metrics|monitoring-loki|monitoring-tempo|observability-alloy|karpenter"
    action        = "drop"
  }  
}

loki.source.kubernetes "kubernetes_pods" {
  targets    = discovery.relabel.kubernetes_pods.output
  forward_to = [loki.write.default.receiver]
}


otelcol.receiver.otlp "default" {
  grpc {}
  http {}

  output {
    traces = [otelcol.connector.spanmetrics.default.input, otelcol.connector.spanlogs.default.input, otelcol.connector.servicegraph.default.input, otelcol.processor.batch.default.input]
    logs = [otelcol.processor.transform.default.input]
    metrics = [otelcol.processor.batch.default.input]
  }
}

otelcol.processor.transform "default" {
  log_statements {
    context = "log"

    statements = [
      `set(attributes["logger_name"], instrumentation_scope.name) where instrumentation_scope.name != nil`,
    ]
  }

  output {
    logs = [otelcol.processor.attributes.default.input]
  }
}

otelcol.processor.attributes "default" {
  action {
    key = "loki.resource.labels"
    action = "insert"
    value = "service.name, service.instance.id, qualified.service.name, container.id, host.name"
  }

  action {
    key = "loki.attribute.labels"
    action = "insert"
    value = "logger_name"
  }

  output {
    metrics = []
    logs = [otelcol.exporter.loki.default.input]
  }
}


otelcol.connector.servicegraph "default" {
  dimensions = ["http.method"]
  output {
    metrics = [otelcol.processor.batch.default.input]
  }
}
otelcol.connector.spanlogs "default" {
  roots           = true
  span_attributes = [
    "http.method",
    "http.target",
    "service.name",            
    "http.flavor",
    "http.host",
    "http.scheme",
    "http.server_name",
    "http.user_agent",
    "net.host.port",
    "net.peer.ip",
    "net.peer.port",
  ]
  labels          = [    
    "http.method",
    "http.target",
    "service.name",            
    "http.flavor",
    "http.host",
    "http.scheme",
    "http.server_name",
    "http.user_agent",
    "net.host.port",
    "net.peer.ip",
    "net.peer.port",       
    ]

  output {
    logs = [otelcol.exporter.loki.default.input]
  }
}

otelcol.connector.spanmetrics "default" {
  dimension {
    name = "http.status_code"
  }  
  dimension {
    name = "service.name"
  }
  dimension {
    name = "host.name"
  }
  dimension {
    name = "qualified.service.name"
    default = "unknown"
  }
  dimension {
    name = "http.route"
    default = "/"
  }
  dimension {
    name = "http.method"
    default = "GET"
  }
  histogram {
    unit = "ms"
    explicit {
      buckets = ["1ms", "5ms", "10ms", "20ms", "50ms", "100ms", "200s", "500ms", "750ms", "1000ms", "1500ms", "2000ms", "5000ms", "10000ms"]
    }
  }
  output {
    metrics = [otelcol.processor.batch.default.input]
  }
}

otelcol.processor.batch "default" {
  output {
    traces  = [otelcol.exporter.otlp.default.input]
    logs = [otelcol.exporter.loki.default.input]
    metrics = [otelcol.exporter.prometheus.default.input]
  }
}


otelcol.exporter.loki "default" {
  forward_to = [loki.write.default.receiver]
}

otelcol.exporter.prometheus "default" {
  forward_to = [prometheus.remote_write.default.receiver]
}


prometheus.remote_write "default" {
    endpoint {
        url = "http://mimir-nginx.monitoring-mimir.svc:80/api/v1/push"
        remote_timeout = "60s"
    }
}


loki.write "default" {
    endpoint {
        url = "http://loki-loki-distributed-gateway.monitoring-loki.svc.cluster.local/loki/api/v1/push"
        remote_timeout = "60s"
    }
}

otelcol.exporter.otlp "default" {
      client {
        endpoint = "http://tempo-distributor.monitoring-tempo.svc.cluster.local:4317"
        tls {
          insecure             = true
          insecure_skip_verify = true
        }
      }
}