alloy:  
  configMap:
    create: true
    # name: alloy-config
    # key: alloy.yaml

  # Add resource limits
  resources:
    requests:
      cpu: 50m
      memory: 456Mi
    limits:
      cpu: 100m
      memory: 550Mi

  extraPorts: 
    - name: otlp-grpc # The default port for otlp-grpc is 4317, so you need to add this port to the service related to your alloy instance
      port: 4317
      targetPort: 4317
      protocol: TCP
    - name: otlp-http # The default port for otlp-http is 4318, so you need to add this port to the service related to your alloy instance
      port: 4318
      targetPort: 4318
      protocol: TCP

controller:
  # Change to deployment for better scalability
  type: 'daemonset'


service:
  # -- Creates a Service for the controller's pods.
  enabled: true
  # -- Service type
  type: ClusterIP
  # -- NodePort port. Only takes effect when `service.type: NodePort`
  nodePort: 31128
  # -- Cluster IP, can be set to None, empty "" or an IP address
  clusterIP: ''
  # -- Value for internal traffic policy. 'Cluster' or 'Local'
  internalTrafficPolicy: Cluster
  annotations: {}
    # cloud.google.com/load-balancer-type: Internal


# Add ingress
ingress:
  enabled: true
  ingressClassName: external-nginx
  annotations:
    kubernetes.io/tls-acme: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
  path: /
  pathType: Prefix
  hosts:
    - alloy.stage-k8s.strawmine.com
  tls:
    - secretName: alloy.stage-k8s.strawmine.com-tls
      hosts:
        - alloy.stage-k8s.strawmine.com
  faroPort: 12345