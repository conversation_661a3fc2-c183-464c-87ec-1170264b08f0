{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "A language-agnostic application performance management(APM) with OpenTelemetry, Grafana, and Prometheus.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 22, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "green", "mode": "fixed"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\", span_kind=\"SPAN_KIND_SERVER\", span_name=~\"$route\"}) by(service_name)", "instant": true, "legendFormat": "{{label_name}}", "range": true, "refId": "A"}], "title": "Total Request", "transformations": [{"id": "seriesToRows", "options": {}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"field": "Time"}]}}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\", span_kind=\"SPAN_KIND_SERVER\", http_status_code=~\"^2.*\", span_name=~\"$route\"}) by(http_status_code)", "format": "time_series", "instant": true, "legendFormat": "Http Status 2XX", "range": true, "refId": "2XX"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\", span_kind=\"SPAN_KIND_SERVER\", http_status_code=~\"^3.*\", http_route=~\"$route\"}) by(http_status_code)", "format": "time_series", "hide": false, "instant": true, "legendFormat": "Http Status 3XX", "range": true, "refId": "3XX"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\", span_kind=\"SPAN_KIND_SERVER\", http_status_code=~\"^4.*\", http_route=~\"$route\"}) by(http_status_code)", "format": "time_series", "hide": false, "instant": true, "legendFormat": "Http Status 4XX", "range": true, "refId": "4XX"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\", span_kind=\"SPAN_KIND_SERVER\", http_status_code=~\"^5.*\", http_route=~\"$route\"}) by(http_status_code)", "format": "time_series", "hide": false, "instant": true, "legendFormat": "Http Status 5XX", "range": true, "refId": "5XX"}], "title": "Requests Count", "transformations": [{"id": "seriesToRows", "options": {}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"field": "Time"}]}}, {"id": "partitionByValues", "options": {"fields": ["Metric"], "keepFields": false}}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "request amount distribution", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": [], "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "id": 4, "options": {"displayLabels": ["percent"], "legend": {"displayMode": "list", "placement": "right", "showLegend": true, "values": ["percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(traces_span_metrics_duration_milliseconds_count{span_kind=\"SPAN_KIND_SERVER\", service_name=\"$app\",   span_name=~\"$route\", }) by(span_name)", "instant": false, "legendFormat": "{{label_name}}", "range": true, "refId": "A"}], "title": "Request Distribution", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "cumulative latency distribution", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": [], "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "id": 7, "options": {"displayLabels": ["percent"], "legend": {"displayMode": "list", "placement": "right", "showLegend": true, "values": ["percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(traces_span_metrics_duration_milliseconds_sum{span_kind=\"SPAN_KIND_SERVER\", service_name=\"$app\", span_name=~\"$route\"}) by(span_name)", "instant": true, "legendFormat": "{{label_name}}", "range": false, "refId": "A"}], "title": "Loading Distribution", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "overall request rate per minute over last 3 minutes", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green"}]}, "unit": "reqpm"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 0, "y": 4}, "id": 8, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(rate(traces_span_metrics_duration_milliseconds_count{span_kind=\"SPAN_KIND_SERVER\", service_name=\"$app\"}[3m])*60)", "hide": false, "instant": false, "range": true, "refId": "B"}], "title": "Overall Request Rate", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "percentage of HTTP status 5xx in all requests", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "red", "value": 10}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 3, "y": 4}, "id": 9, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(traces_span_metrics_duration_milliseconds_count{span_kind=\"SPAN_KIND_SERVER\", service_name=\"$app\", http_status_code=~\"5.*|\", span_name=~\"$route\"})/sum(traces_span_metrics_duration_milliseconds_count{span_kind=\"SPAN_KIND_SERVER\", service_name=\"$app\", span_name=~\"$route\"})", "instant": false, "range": true, "refId": "A"}], "title": "Overall Error Rate", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "request rate per minute over last 3 minutes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "reqpm"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["GET /admin/api/v1/status/report"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 8}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "sum(rate(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\", span_kind=\"SPAN_KIND_SERVER\", span_name=~\"$route\"}[3m])*60)  by(span_name)", "hide": false, "instant": false, "legendFormat": "{{label_name}}", "range": true, "refId": "B"}], "title": "Request Rate", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "PR95 latency over last 3 minutes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 8}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(traces_span_metrics_duration_milliseconds_bucket{span_kind=\"SPAN_KIND_SERVER\", service_name=\"$app\", span_name=~\"$route\"}[3m])) by (le, span_name))", "instant": false, "legendFormat": "{{label_name}}", "range": true, "refId": "A"}], "title": "PR95 Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "by route and http status code", "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 8}, "id": 6, "options": {"displayMode": "lcd", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "valueMode": "color"}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sort_desc(traces_span_metrics_duration_milliseconds_sum{span_kind=\"SPAN_KIND_SERVER\", service_name=\"$app\", http_status_code!=\"\", span_name=~\"$route\"} / traces_span_metrics_duration_milliseconds_count{span_kind=\"SPAN_KIND_SERVER\", service_name=\"$app\", http_status_code!=\"\", span_name=~\"$route\"})", "instant": true, "legendFormat": "[{{http_status_code}}] {{span_name}}", "range": false, "refId": "A"}], "title": "Average Latency", "type": "bargauge"}, {"datasource": {"type": "tempo", "uid": "P8D6546721A1D106C"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 16}, "id": 13, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "tempo", "uid": "P8D6546721A1D106C"}, "limit": 20, "metricsQueryType": "range", "query": "{kind=server && resource.service.name=\"$app\" && name=~\"$route\"}", "queryType": "traceql", "refId": "A", "tableType": "traces"}], "title": "Panel Title", "transformations": [{"id": "convertFieldType", "options": {"conversions": [{}], "fields": {}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "Details of each API", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Request Rate"}, "properties": [{"id": "custom.cellOptions", "value": {"type": "sparkline"}}, {"id": "color", "value": {"mode": "palette-classic"}}, {"id": "unit", "value": "reqpm"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Error Rate"}, "properties": [{"id": "custom.cellOptions", "value": {"spanNulls": false, "type": "sparkline"}}, {"id": "color", "value": {"mode": "palette-classic"}}, {"id": "unit", "value": "percentunit"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "PR95"}, "properties": [{"id": "custom.cellOptions", "value": {"hideValue": false, "type": "sparkline"}}, {"id": "color", "value": {"mode": "palette-classic"}}, {"id": "unit", "value": "ms"}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 25}, "id": 10, "options": {"cellHeight": "sm", "footer": {"countRows": false, "enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(rate(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\", span_name=~\"$route\", span_kind=\"SPAN_KIND_SERVER\"}[3m])*60) by(service_name, http_method, span_name)", "format": "time_series", "hide": false, "instant": false, "range": true, "refId": "Request Rate"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(traces_span_metrics_duration_milliseconds_bucket{service_name=\"$app\", span_name=~\"$route\", span_kind=\"SPAN_KIND_SERVER\"}[3m])) by (le, service_name, http_method, span_name))", "hide": false, "instant": false, "range": true, "refId": "PR95"}, {"datasource": {"type": "prometheus", "uid": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "sum(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\", span_name=~\"$route\", span_kind=\"SPAN_KIND_SERVER\", http_status_code!~\"2.*|3.*\"}) by(service_name, http_method, span_name) / sum(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\", span_name=~\"$route\", span_kind=\"SPAN_KIND_SERVER\"}) by(service_name, http_method, span_name)", "format": "time_series", "hide": false, "instant": false, "range": true, "refId": "Error Rate"}], "title": "Details", "transformations": [{"id": "timeSeriesTable", "options": {}}, {"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "includeByName": {}, "indexByName": {"Time": 0, "Trend #PR95 Trend": 7, "Trend #Request Rate Trend": 5, "Value #PR95": 8, "Value #Request Rate": 6, "http_method": 3, "http_route": 2, "http_status_code": 4, "service_name": 1}, "renameByName": {"Trend": "Request Rate Trend", "Trend #Error Rate": "Error Rate", "Trend #Error Rate Trend": "Error Rate Trend", "Trend #PR95": "PR95", "Trend #PR95 Trend": "PR95 Latency Trend", "Trend #Request Rate": "Request Rate", "Trend #Request Rate Trend": "Request Rate Trend", "Value": "Request Rate", "Value #A": "Error Rate", "Value #Error Rate": "Error Rate", "Value #PR95": "PR95 Latency", "Value #Request Rate": "Request Rate", "http_method": "Method", "http_route": "Route", "http_status_code": "Status Code", "service_name": "Application"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "bRate"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "lcd", "type": "gauge"}}, {"id": "color", "value": {"mode": "continuous-BlYlRd"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "e rate"}, "properties": [{"id": "color", "value": {"mode": "continuous-RdYlGr"}}, {"id": "custom.cellOptions", "value": {"mode": "lcd", "type": "gauge"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Rate"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "", "url": "/grafana/explore?${__url_time_range},\"queries\":\"sum(rate(traces_spanmetrics_calls_total{span_name=\"${__data.fields[0]}\"}[$__rate_interval]))\""}]}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 34}, "id": 12, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "exemplar": false, "expr": "topk(5, sum(rate(traces_span_metrics_calls_total{service_name=\"$app\"}[$__range])) by (span_name)) ", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "Rate"}, {"datasource": {"type": "prometheus", "uid": "webstore-metrics"}, "editorMode": "code", "exemplar": false, "expr": "topk(5, sum(rate(traces_span_metrics_calls_total{service_name=\"$app\",status_code=\"STATUS_CODE_ERROR\"}[$__range])) by (span_name))", "format": "table", "instant": true, "legendFormat": "__auto", "range": false, "refId": "Error Rate"}], "title": "Tempo APM table", "transformations": [{"id": "joinByField", "options": {"byField": "span_name", "mode": "outer"}}, {"id": "organize", "options": {"excludeByName": {"Time 1": true, "Time 2": true, "Time 3": true}, "indexByName": {"Time 1": 1, "Time 2": 4, "Time 3": 6, "Value #BarRate": 3, "Value #Error Rate": 5, "Value #Rate": 2, "span_name": 0}, "renameByName": {"Value #Error Rate": "Error Rate", "Value #Rate": "Rate"}}}, {"id": "calculateField", "options": {"alias": "bRate", "mode": "reduceRow", "reduce": {"include": ["Value #Rate"], "reducer": "sum"}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Error Rate": 3, "Rate": 1, "bRate": 2, "span_name": 0}, "renameByName": {"bRate": " rate", "span_name": "Name"}}}, {"id": "calculateField", "options": {"alias": "e rate", "mode": "reduceRow", "reduce": {"include": ["Error Rate"], "reducer": "sum"}}}], "type": "table"}, {"datasource": {"type": "tempo", "uid": "P8D6546721A1D106C"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 14, "w": 24, "x": 0, "y": 42}, "id": 11, "options": {"edges": {}, "nodes": {}, "zoomMode": "cooperative"}, "pluginVersion": "11.6.1", "targets": [{"datasource": {"type": "tempo", "uid": "P8D6546721A1D106C"}, "filters": [{"id": "5cc08e47", "operator": "=", "scope": "span"}, {"id": "service-name", "operator": "=", "scope": "resource", "tag": "service.name", "value": ["kalpha-n-data-integrations"], "valueType": "string"}, {"id": "span-name", "operator": "=", "scope": "span", "tag": "name", "value": ["GET /dataservices/download"], "valueType": "string"}, {"id": "status", "operator": "=", "scope": "intrinsic", "tag": "status", "valueType": "keyword"}], "hide": false, "limit": 20, "metricsQueryType": "range", "query": "{resource.service.name=\"$app\" && resource.http.route=\"$route\"}", "queryType": "serviceMap", "refId": "A", "tableType": "traces"}], "title": "Panel Title", "type": "nodeGraph"}], "preload": false, "refresh": "", "schemaVersion": 41, "tags": [], "templating": {"list": [{"current": {"text": "kalpha-n-data-integrations", "value": "kalpha-n-data-integrations"}, "datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "definition": "label_values(traces_span_metrics_duration_milliseconds_count,job)", "includeAll": false, "label": "Application", "name": "app", "options": [], "query": {"qryType": 1, "query": "label_values(traces_span_metrics_duration_milliseconds_count,job)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "definition": "label_values(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\"},span_name)", "includeAll": true, "label": "Route", "multi": true, "name": "route", "options": [], "query": {"qryType": 1, "query": "label_values(traces_span_metrics_duration_milliseconds_count{service_name=\"$app\"},span_name)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "OpenTelemetry APM", "uid": "opentelemetry-apm", "version": 9}