useStatefulSet: false
replicas: 1
adminUser: admin
adminPassword: admin@123
tolerations:
- key: "workload"
  operator: "Equal"
  value: "ondemand-general"
  effect: "NoSchedule"

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: "nodepool"
              operator: In
              values:
                - "staging-solvei8-amd-on-demand-general-purpose"

resources:
  requests:
    cpu: 150m
    memory: 312Mi
  limits:
    cpu: 300m
    memory: 500Mi

datasources:
  datasources.yaml:
    apiVersion: 1
    datasources:
    - name: Mimir
      type: prometheus
      url: http://mimir-nginx.monitoring-mimir.svc:80/prometheus
      access: proxy
      isDefault: true
      editable: true

    - name: Loki
      type: loki
      access: proxy
      editable: true
      url: http://loki-loki-distributed-gateway.monitoring-loki.svc.cluster.local
      isDefault: false

    - name: tempo
      type: tempo
      access: proxy
      editable: true
      url: http://tempo-query-frontend-discovery.monitoring-tempo.svc.cluster.local:3100
      isDefault: false
      jsonData:
        tracesToLogsV2:
          # Field with an internal link pointing to a logs data source in Grafana.
          # datasourceUid value must match the uid value of the logs data source.
          datasourceUid: 'Loki'
          spanStartTimeShift: '-2m'
          spanEndTimeShift: '2m'
          filterByTraceID: false
          filterBySpanID: false
          customQuery: true
          query: '{exporter="OTLP"} |= "$${__trace.traceId}"'
        serviceMap:
          datasourceUid: 'Mimir'
        nodeGraph:
          enabled: true

dashboardProviders:
  dashboardproviders.yaml:
    apiVersion: 1
    providers:
    - name: 'default'
      orgId: 1
      folder: ''
      type: file
      disableDeletion: false
      editable: true
      options:
        path: /var/lib/grafana/dashboards/default

dashboards:
  default:
    jmx-exporter:
      gnetId: 14845
      datasource: Mimir    
    # node-exporter:
    #   gnetId: 12486
    #   datasource: Mimir
    # node-exporter-1:
    #   gnetId: 1860
    #   datasource: Mimir
    # node-exporter:
    #   file: dashboards/node-exporter.json
    #   datasource: Mimir
    blackbox-exporter:
      gnetId: 7587
      datasource: Mimir
    nginx:
      file: dashboards/nginx.json
      datasource: Mimir

# Mount the secret as an environment variable
envFromSecrets:
  - name: grafana-slack-webhook
    key: url
  - name: grafana-oauth-credentials
    key: GCP_CLIENT_ID
  - name: grafana-oauth-credentials
    key: GCP_CLIENT_SECRET

alerting:
  contactpoints.yaml:
    apiVersion: 1
    contactPoints:
      - orgId: 1
        name: "Slack Alerts"
        receivers:
          - uid: "slack-test"
            type: "slack"
            settings:
              url: "${url}"           
              title: '{{ `{{ .CommonLabels.alertname }}` }}'
              text: '{{ `{{ .CommonAnnotations.summary }}\n{{ .CommonAnnotations.description }}` }}'
grafana.ini:
  users:
    viewers_can_edit: true
  auth:
    disable_login_form: false
  server:
    root_url: https://grafana.stage-k8s.strawmine.com
  auth.google:
    enabled: true
    allow_sign_up: true
    auto_login: false
    client_id: "${GCP_CLIENT_ID}"
    client_secret: "${GCP_CLIENT_SECRET}"
    auth_url: "https://accounts.google.com/o/oauth2/v2/auth"
    token_url: "https://oauth2.googleapis.com/token"
    api_url: "https://openidconnect.googleapis.com/v1/userinfo"
    allowed_domains: "solvei8.com"

persistence:
  enabled: true
  storageClassName: ebs-sc-delete
  size: 5Gi
  accessModes:
    - ReadWriteOnce

podDisruptionBudget:
  minAvailable: 1

deploymentStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 1

ingress:
  enabled: true
  ingressClassName: external-nginx
  annotations:
    nginx.ingress.kubernetes.io/enable-opentelemetry: "true"
    cert-manager.io/cluster-issuer: letsencrypt-issuer

  path: /
  pathType: Prefix
  hosts:
    - grafana.stage-k8s.strawmine.com
  tls:
    - secretName: grafana.stage-k8s.strawmine.com-tls
      hosts:
        - grafana.stage-k8s.strawmine.com
