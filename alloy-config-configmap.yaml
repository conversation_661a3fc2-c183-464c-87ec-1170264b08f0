apiVersion: v1
kind: ConfigMap
metadata:
  name: alloy-config
  namespace: observability-alloy  # Change to your desired namespace
data:
  alloy.yaml: |
    logging {
      level  = "info"
      format = "logfmt"
    }

    discovery.kubernetes "cadvisor_service" {
      role = "endpointslice"
    }

    discovery.kubernetes "cadvisor_pods" {
      role = "node"
    }

    discovery.relabel "cadvisor_pods" {
      targets = discovery.kubernetes.cadvisor_pods.targets

      rule {
        source_labels = ["__meta_kubernetes_node_name"]
        target_label  = "__address__"
        regex = "(.+)"
        replacement  = "${1}:10250"
      }

      rule {
        target_label = "__scheme__"
        replacement = "https"
      }

      rule {
        target_label = "__metrics_path__"
        replacement = "/metrics/cadvisor"
      }
      rule {
        source_labels = ["__meta_kubernetes_node_name"]
        target_label  = "instance"
      }
    }

    prometheus.scrape "cadvisor" {
      targets = discovery.relabel.cadvisor_pods.output
      scheme = "https"
      job_name = "cadvisor"
      forward_to = [prometheus.remote_write.default.receiver]

      tls_config {
        insecure_skip_verify = true
      }

      authorization {
        type = "Bearer"
        credentials_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
      }
    }


    discovery.kubernetes "kube_state_metrics" {
      role = "endpoints"

      selectors {
        role      = "endpoints"
        label     = "app.kubernetes.io/name=kube-state-metrics"
      }
    }

    prometheus.scrape "kube_state_metrics" {
      targets        = discovery.kubernetes.kube_state_metrics.targets
      scrape_interval = "30s"
      metrics_path    = "/metrics"
      scheme          = "http"
      forward_to = [prometheus.remote_write.default.receiver]
      job_name   = "kube-state-metrics"
    }

    discovery.kubernetes "endpointslice" {
      role = "endpointslice"
    }

    discovery.relabel "node_exporter" {
      targets = discovery.kubernetes.endpointslice.targets

      rule {
        source_labels = ["__meta_kubernetes_endpointslice_port"]
        regex         = "9100"
        action        = "keep"
      }
      rule {
        source_labels = ["__meta_kubernetes_pod_name"]
        target_label  = "pod"
      }
      rule {
        source_labels = ["__meta_kubernetes_pod_node_name"]
        target_label  = "instance"
      }
    }
    prometheus.scrape "node_exporter" {
      targets    = discovery.relabel.node_exporter.output
      forward_to = [prometheus.remote_write.default.receiver]
      job_name   = "node_exporter"
    }

    discovery.relabel "jmx_metrics" {
      targets = discovery.kubernetes.endpointslice.targets

      rule {
        source_labels = ["__meta_kubernetes_endpointslice_port"]
        regex         = "9050"
        action        = "keep"
      }
      rule {
        source_labels = ["__meta_kubernetes_pod_name"]
        target_label  = "instance"
      }
      rule {
        source_labels = ["__meta_kubernetes_pod_node_name"]
        target_label  = "node"
      }
    }

    prometheus.scrape "jmx_metrics" {
      targets    = discovery.relabel.jmx_metrics.output
      forward_to = [prometheus.remote_write.default.receiver]
      job_name   = "jmx"
    }


    discovery.kubernetes "kubernetes_pods" {
      role = "pod"
    }

    discovery.kubernetes "ingress_all" {
      role = "ingress"
    }

    
    discovery.relabel "blackbox_targets" {
    targets = discovery.kubernetes.endpointslice.targets

    rule {
      source_labels = ["__meta_kubernetes_endpointslice_port"]
      regex         = "80|443"
      action        = "keep"
    }

    rule {
      source_labels = ["__address__"]
      target_label  = "__param_target"
    }

    rule {
      source_labels = ["__meta_kubernetes_service_name"]
      target_label  = "name"
    }

    rule {
      target_label = "__address__"
      replacement  = "*************:9115"
    }
    rule {
    source_labels = ["__meta_kubernetes_namespace"]
    target_label  = "namespace"
    }
    rule {
      source_labels = ["__meta_kubernetes_service_name"]
      target_label  = "kubernetes_service"
    }
    rule {
      source_labels = ["__param_target"]
      target_label  = "instance"
    }
    rule {
      source_labels = ["__meta_kubernetes_service_label_app_kubernetes_io_name"]
      target_label  = "app"
    }
    rule{
      source_labels = ["__meta_kubernetes_endpointslice_label_kubernetes_io_service_name"]
      target_label  = "service"
    }
    rule {
      source_labels = ["__meta_kubernetes_namespace"]
      regex  = "stage-backend"
      action = "keep"
    }

      }

    prometheus.exporter.blackbox "blackbox" {
      config = "{ modules: { http_2xx: { prober: http, timeout: 5s } } }"
      targets = discovery.relabel.blackbox_targets.output
    }

    prometheus.scrape "blackbox" {
      targets    = prometheus.exporter.blackbox.blackbox.targets
      forward_to = [prometheus.remote_write.default.receiver]
      job_name   = "blackbox"
      metrics_path = "/admin/api/v1/status/report"
    }

    discovery.relabel "kubernetes_pods" {
      targets = discovery.kubernetes.kubernetes_pods.targets
      rule {
        source_labels = ["__meta_kubernetes_pod_node_name"]
        target_label  = "node_name"
      }
      rule {
        source_labels = ["__meta_kubernetes_namespace"]
        target_label  = "namespace"
      }
      rule {
        source_labels = ["__meta_kubernetes_pod_name"]
        target_label  = "pod"
      }
      rule {
        source_labels = ["__meta_kubernetes_pod_container_name"]
        target_label  = "container"
      }
      rule {
        source_labels = ["__meta_kubernetes_namespace"]
        regex         = "ingress"
        action        = "keep"
      }  
    }

    loki.source.kubernetes "kubernetes_pods" {
      targets    = discovery.relabel.kubernetes_pods.output
      forward_to = [loki.write.default.receiver]
    }



    otelcol.receiver.otlp "default" {
      grpc {}
      http {}

      output {
        traces = [otelcol.connector.spanmetrics.default.input, otelcol.connector.spanlogs.default.input, otelcol.processor.batch.default.input]
        logs = [otelcol.processor.batch.default.input]
        metrics = [otelcol.processor.batch.default.input]
      }
    }

    otelcol.connector.spanlogs "default" {
      roots           = true
      span_attributes = ["http.method", "http.target", "service.name", "db.name"]
      labels          = ["http.status_code", "k8s.pod.name"]

      output {
        logs = [otelcol.exporter.loki.default.input]
      }
    }

    otelcol.connector.spanmetrics "default" {
      dimension {
        name = "http.status_code"
      }
      dimension {
        name = "http.method"
        default = "GET"
      }
      histogram {
        unit = "s"
        explicit {
          buckets = ["333ms", "777s", "999h"]
        }
      }
      output {
        metrics = [otelcol.exporter.prometheus.default.input]
      }
    }

    otelcol.processor.batch "default" {
      output {
        traces  = [otelcol.exporter.otlp.default.input]
        logs = [otelcol.exporter.loki.default.input]
        metrics = [otelcol.exporter.prometheus.default.input]
      }
    }

    otelcol.exporter.loki "default" {
      forward_to = [loki.write.default.receiver]
    }

    otelcol.exporter.prometheus "default" {
      forward_to = [prometheus.remote_write.default.receiver]
    }


    prometheus.remote_write "default" {
        endpoint {
            url = "http://mimir-nginx.monitoring-mimir.svc:80/api/v1/push"
            remote_timeout = "60s"
        }
    }

    loki.write "default" {
        endpoint {
            url = "http://loki-loki-distributed-gateway.monitoring-loki.svc.cluster.local/loki/api/v1/push"
            remote_timeout = "60s"
        }
    }

    otelcol.exporter.otlp "default" {
          client {
            endpoint = "http://tempo-distributor.monitoring-tempo.svc.cluster.local:4317"
            tls {
              insecure             = true
              insecure_skip_verify = true
            }
          }
    }